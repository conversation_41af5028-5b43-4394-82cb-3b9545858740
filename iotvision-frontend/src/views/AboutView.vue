<template>
  <div class="about-view">
    <section class="about-hero section">
      <div class="container">
        <div class="hero-content">
          <h1 class="hero-title">
            {{ $t('about.title') }}
          </h1>
          <p class="hero-subtitle">
            {{ $t('about.subtitle') }}
          </p>
        </div>
      </div>
    </section>

    <section class="about-story section">
      <div class="container">
        <div class="story-content">
          <div class="story-text">
            <h2>Our Story</h2>
            <p>
              Founded in 2020 by a team of visionary engineers and data scientists, IoTVision emerged
              from a simple yet powerful belief: that the future belongs to those who can seamlessly
              connect the physical and digital worlds.
            </p>
            <p>
              Today, we're proud to be at the forefront of the IoT revolution, helping businesses
              across industries transform their operations through intelligent connectivity and
              data-driven insights.
            </p>
          </div>

          <div class="story-stats">
            <div class="stat-card">
              <div class="stat-number">500+</div>
              <div class="stat-label">Projects Completed</div>
            </div>
            <div class="stat-card">
              <div class="stat-number">50M+</div>
              <div class="stat-label">Devices Connected</div>
            </div>
            <div class="stat-card">
              <div class="stat-number">25+</div>
              <div class="stat-label">Countries Served</div>
            </div>
          </div>
        </div>
      </div>
    </section>

    <section class="about-mission section">
      <div class="container">
        <div class="mission-grid">
          <div class="mission-item">
            <div class="mission-icon">🎯</div>
            <h3>{{ $t('about.mission.title') }}</h3>
            <p>
              {{ $t('about.mission.description') }}
            </p>
          </div>

          <div class="mission-item">
            <div class="mission-icon">👁️</div>
            <h3>{{ $t('about.vision.title') }}</h3>
            <p>
              {{ $t('about.vision.description') }}
            </p>
          </div>

          <div class="mission-item">
            <div class="mission-icon">⚡</div>
            <h3>Our Values</h3>
            <p>
              Innovation, reliability, and customer success drive everything we do.
              We believe in building lasting partnerships and delivering exceptional value.
            </p>
          </div>
        </div>
      </div>
    </section>

    <section class="about-team section">
      <div class="container">
        <h2 class="section-title">Meet Our Team</h2>
        <div class="team-grid">
          <div v-for="member in teamMembers" :key="member.id" class="team-card">
            <div class="member-avatar">{{ member.avatar }}</div>
            <h3 class="member-name">{{ member.name }}</h3>
            <p class="member-role">{{ member.role }}</p>
            <p class="member-bio">{{ member.bio }}</p>
          </div>
        </div>
      </div>
    </section>
  </div>
</template>

<script setup lang="ts">
const teamMembers = [
  {
    id: 1,
    name: 'Alex Chen',
    role: 'CEO & Co-Founder',
    avatar: '👨‍💼',
    bio: 'Visionary leader with 15+ years in IoT and enterprise technology.'
  },
  {
    id: 2,
    name: 'Sarah Johnson',
    role: 'CTO & Co-Founder',
    avatar: '👩‍💻',
    bio: 'Technical architect specializing in scalable IoT infrastructure.'
  },
  {
    id: 3,
    name: 'Michael Rodriguez',
    role: 'Head of AI/ML',
    avatar: '👨‍🔬',
    bio: 'AI researcher focused on intelligent edge computing solutions.'
  },
  {
    id: 4,
    name: 'Emily Zhang',
    role: 'Head of Security',
    avatar: '👩‍🔒',
    bio: 'Cybersecurity expert ensuring enterprise-grade protection.'
  }
]
</script>

<style scoped lang="scss">
.about-view {
  padding-top: 70px;
}

.about-hero {
  background: linear-gradient(135deg, rgba(26, 26, 46, 0.8) 0%, rgba(10, 10, 15, 0.9) 100%);
  text-align: center;
  padding: 4rem 0;
}

.hero-content {
  max-width: 800px;
  margin: 0 auto;
}

.hero-title {
  font-size: 3rem;
  margin-bottom: 1.5rem;

  @media (max-width: $breakpoint-md) {
    font-size: 2.5rem;
  }
}

.hero-subtitle {
  font-size: 1.3rem;
  color: $color-text-muted;
  line-height: 1.6;
}

.story-content {
  display: grid;
  grid-template-columns: 2fr 1fr;
  gap: 3rem;
  align-items: center;

  @media (max-width: $breakpoint-lg) {
    grid-template-columns: 1fr;
    gap: 2rem;
  }
}

.story-text {
  h2 {
    font-size: 2rem;
    margin-bottom: 1.5rem;
    color: $color-text-white;
  }

  p {
    color: $color-text-muted;
    line-height: 1.6;
    margin-bottom: 1rem;
    font-size: 1.1rem;
  }
}

.story-stats {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;

  @media (max-width: $breakpoint-lg) {
    flex-direction: row;
    justify-content: center;
  }

  @media (max-width: $breakpoint-sm) {
    flex-direction: column;
  }
}

.stat-card {
  background: rgba(26, 26, 46, 0.6);
  border: 1px solid rgba(0, 255, 255, 0.2);
  border-radius: $border-radius-lg;
  padding: 1.5rem;
  text-align: center;

  .stat-number {
    font-family: $font-heading;
    font-size: 2rem;
    font-weight: 700;
    color: $color-neon-aqua;
    text-shadow: 0 0 10px currentColor;
    margin-bottom: 0.5rem;
  }

  .stat-label {
    font-size: 0.9rem;
    color: $color-text-muted;
    text-transform: uppercase;
    letter-spacing: 0.05em;
  }
}

.mission-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 2rem;
}

.mission-item {
  background: rgba(26, 26, 46, 0.6);
  border: 1px solid rgba(0, 255, 255, 0.2);
  border-radius: $border-radius-lg;
  padding: 2rem;
  text-align: center;
  transition: all 0.3s ease;

  &:hover {
    border-color: rgba(0, 255, 255, 0.4);
    transform: translateY(-5px);
    box-shadow: 0 10px 30px rgba(0, 255, 255, 0.2);
  }

  .mission-icon {
    font-size: 3rem;
    margin-bottom: 1rem;
  }

  h3 {
    color: $color-text-white;
    margin-bottom: 1rem;
    font-size: 1.3rem;
  }

  p {
    color: $color-text-muted;
    line-height: 1.6;
  }
}

.section-title {
  text-align: center;
  font-size: 2.5rem;
  margin-bottom: 3rem;
  color: $color-text-white;
}

.team-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 2rem;
}

.team-card {
  background: rgba(26, 26, 46, 0.6);
  border: 1px solid rgba(0, 255, 255, 0.2);
  border-radius: $border-radius-lg;
  padding: 2rem;
  text-align: center;
  transition: all 0.3s ease;

  &:hover {
    border-color: rgba(0, 255, 255, 0.4);
    transform: translateY(-5px);
    box-shadow: 0 10px 30px rgba(0, 255, 255, 0.2);
  }
}

.member-avatar {
  font-size: 4rem;
  margin-bottom: 1rem;
}

.member-name {
  color: $color-text-white;
  margin-bottom: 0.5rem;
  font-size: 1.2rem;
}

.member-role {
  color: $color-neon-aqua;
  margin-bottom: 1rem;
  font-weight: 500;
}

.member-bio {
  color: $color-text-muted;
  line-height: 1.5;
  font-size: 0.9rem;
}
</style>
