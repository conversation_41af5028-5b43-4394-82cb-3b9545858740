// Main SCSS entry point for IoTVision

// Import variables first
@use 'variables' as *;

// Import base styles
@use 'base' with ($font-body: $font-body, $font-heading: $font-heading, $color-text-light: $color-text-light, $color-primary-bg: $color-primary-bg, $color-dark-blue: $color-dark-blue, $color-neon-aqua: $color-neon-aqua, $color-neon-green: $color-neon-green, $color-neon-fuchsia: $color-neon-fuchsia, $color-text-white: $color-text-white, $border-radius-full: $border-radius-full, $font-size-base: $font-size-base);

// Import typography
@use 'typography' with ($font-heading: $font-heading, $font-body: $font-body, $font-code: $font-code, $color-text-light: $color-text-light, $color-text-white: $color-text-white, $color-text-muted: $color-text-muted, $color-neon-aqua: $color-neon-aqua, $color-neon-green: $color-neon-green, $gradient-text: $gradient-text, $spacing-md: $spacing-md, $font-size-5xl: $font-size-5xl, $font-size-4xl: $font-size-4xl, $font-size-3xl: $font-size-3xl, $font-size-2xl: $font-size-2xl, $font-size-xl: $font-size-xl, $font-size-lg: $font-size-lg, $font-size-sm: $font-size-sm, $breakpoint-md: $breakpoint-md, $duration-fast: $duration-fast, $border-radius-sm: $border-radius-sm, $color-dark-blue: $color-dark-blue, $border-radius-md: $border-radius-md);

// Import animations
@use 'animations' with ($duration-slow: $duration-slow, $color-neon-aqua: $color-neon-aqua);

// Global utility classes
.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 $spacing-lg;
  
  @media (max-width: $breakpoint-sm) {
    padding: 0 $spacing-md;
  }
}

.section {
  padding: $spacing-3xl 0;
  
  @media (max-width: $breakpoint-md) {
    padding: $spacing-2xl 0;
  }
}

.grid {
  display: grid;
  gap: $spacing-lg;
}

.flex {
  display: flex;
}

.flex-col {
  flex-direction: column;
}

.items-center {
  align-items: center;
}

.justify-center {
  justify-content: center;
}

.justify-between {
  justify-content: space-between;
}

.text-center {
  text-align: center;
}

// Button base styles
.btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: $spacing-md $spacing-xl;
  border-radius: $border-radius-md;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.05em;
  transition: all $duration-normal ease;
  cursor: pointer;
  border: 2px solid transparent;
  position: relative;
  overflow: hidden;
  
  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.1), transparent);
    transition: left $duration-slow ease;
  }
  
  &:hover::before {
    left: 100%;
  }
  
  &:focus {
    outline: 2px solid $color-neon-aqua;
    outline-offset: 2px;
  }
}

.btn-primary {
  background: $gradient-neon;
  color: $color-primary-bg;
  font-weight: 700;
  text-decoration: none;

  &:hover {
    transform: translateY(-2px);
    box-shadow: $shadow-neon;
    color: $color-primary-bg; // Ensure text stays dark on hover
  }

  &:visited {
    color: $color-primary-bg;
  }
}

.btn-outline {
  background: transparent;
  color: $color-neon-aqua;
  border-color: $color-neon-aqua;
  
  &:hover {
    background: $color-neon-aqua;
    color: $color-primary-bg;
    box-shadow: $shadow-neon;
  }
}

.btn-ghost {
  background: transparent;
  color: $color-text-light;
  
  &:hover {
    color: $color-neon-aqua;
    text-shadow: 0 0 10px currentColor;
  }
}

// Card styles
.card {
  background: rgba(26, 26, 46, 0.8);
  border: 1px solid rgba(0, 255, 255, 0.2);
  border-radius: $border-radius-lg;
  padding: $spacing-xl;
  backdrop-filter: blur(10px);
  transition: all $duration-normal ease;
  
  &:hover {
    border-color: rgba(0, 255, 255, 0.4);
    box-shadow: $shadow-neon;
    transform: translateY(-5px);
  }
}

// Loading spinner
.spinner {
  width: 40px;
  height: 40px;
  border: 3px solid rgba(0, 255, 255, 0.3);
  border-top: 3px solid $color-neon-aqua;
  border-radius: 50%;
  animation: rotate-360 1s linear infinite;
}

// Responsive utilities
.hidden {
  display: none;
}

@media (max-width: $breakpoint-sm) {
  .hidden-mobile {
    display: none;
  }
}

@media (min-width: $breakpoint-md) {
  .hidden-desktop {
    display: none;
  }
}
