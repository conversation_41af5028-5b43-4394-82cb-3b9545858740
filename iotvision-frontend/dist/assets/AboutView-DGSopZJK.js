import{d as r,c as o,a as s,i as l,t as e,F as c,r as v,o as n,_ as b}from"./index-DNqMNZKj.js";const u={class:"about-view"},h={class:"about-hero section"},m={class:"container"},p={class:"hero-content"},f={class:"hero-title"},_={class:"hero-subtitle"},g={class:"about-mission section"},y={class:"container"},C={class:"mission-grid"},w={class:"mission-item"},I={class:"mission-item"},T={class:"about-team section"},V={class:"container"},S={class:"team-grid"},$={class:"member-avatar"},A={class:"member-name"},F={class:"member-role"},M={class:"member-bio"},O=r({__name:"AboutView",setup(k){const d=[{id:1,name:"<PERSON>",role:"CEO & Co-Founder",avatar:"👨‍💼",bio:"Visionary leader with 15+ years in IoT and enterprise technology."},{id:2,name:"<PERSON>",role:"CTO & Co-Founder",avatar:"👩‍💻",bio:"Technical architect specializing in scalable IoT infrastructure."},{id:3,name:"<PERSON>",role:"Head of AI/ML",avatar:"👨‍🔬",bio:"AI researcher focused on intelligent edge computing solutions."},{id:4,name:"Emily Zhang",role:"Head of Security",avatar:"👩‍🔒",bio:"Cybersecurity expert ensuring enterprise-grade protection."}];return(i,t)=>(n(),o("div",u,[s("section",h,[s("div",m,[s("div",p,[s("h1",f,e(i.$t("about.title")),1),s("p",_,e(i.$t("about.subtitle")),1)])])]),t[4]||(t[4]=l('<section class="about-story section" data-v-bf72be28><div class="container" data-v-bf72be28><div class="story-content" data-v-bf72be28><div class="story-text" data-v-bf72be28><h2 data-v-bf72be28>Our Story</h2><p data-v-bf72be28> Founded in 2020 by a team of visionary engineers and data scientists, IoTVision emerged from a simple yet powerful belief: that the future belongs to those who can seamlessly connect the physical and digital worlds. </p><p data-v-bf72be28> Today, we&#39;re proud to be at the forefront of the IoT revolution, helping businesses across industries transform their operations through intelligent connectivity and data-driven insights. </p></div><div class="story-stats" data-v-bf72be28><div class="stat-card" data-v-bf72be28><div class="stat-number" data-v-bf72be28>500+</div><div class="stat-label" data-v-bf72be28>Projects Completed</div></div><div class="stat-card" data-v-bf72be28><div class="stat-number" data-v-bf72be28>50M+</div><div class="stat-label" data-v-bf72be28>Devices Connected</div></div><div class="stat-card" data-v-bf72be28><div class="stat-number" data-v-bf72be28>25+</div><div class="stat-label" data-v-bf72be28>Countries Served</div></div></div></div></div></section>',1)),s("section",g,[s("div",y,[s("div",C,[s("div",w,[t[0]||(t[0]=s("div",{class:"mission-icon"},"🎯",-1)),s("h3",null,e(i.$t("about.mission.title")),1),s("p",null,e(i.$t("about.mission.description")),1)]),s("div",I,[t[1]||(t[1]=s("div",{class:"mission-icon"},"👁️",-1)),s("h3",null,e(i.$t("about.vision.title")),1),s("p",null,e(i.$t("about.vision.description")),1)]),t[2]||(t[2]=s("div",{class:"mission-item"},[s("div",{class:"mission-icon"},"⚡"),s("h3",null,"Our Values"),s("p",null," Innovation, reliability, and customer success drive everything we do. We believe in building lasting partnerships and delivering exceptional value. ")],-1))])])]),s("section",T,[s("div",V,[t[3]||(t[3]=s("h2",{class:"section-title"},"Meet Our Team",-1)),s("div",S,[(n(),o(c,null,v(d,a=>s("div",{key:a.id,class:"team-card"},[s("div",$,e(a.avatar),1),s("h3",A,e(a.name),1),s("p",F,e(a.role),1),s("p",M,e(a.bio),1)])),64))])])])]))}}),E=b(O,[["__scopeId","data-v-bf72be28"]]);export{E as default};
