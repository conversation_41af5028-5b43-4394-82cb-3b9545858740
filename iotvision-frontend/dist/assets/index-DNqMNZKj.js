const __vite__mapDeps=(i,m=__vite__mapDeps,d=(m.f||(m.f=["assets/SolutionsView-BZcr96vX.js","assets/SolutionsView-DCBIKbuS.css","assets/TechnologiesView-CghOwnN5.js","assets/TechnologiesView-BqbSPw0z.css","assets/AboutView-DGSopZJK.js","assets/AboutView-h6lTCCJ-.css","assets/ContactView-BkdETCjs.js","assets/ContactView-C2QStUxf.css"])))=>i.map(i=>d[i]);
(function(){const t=document.createElement("link").relList;if(t&&t.supports&&t.supports("modulepreload"))return;for(const o of document.querySelectorAll('link[rel="modulepreload"]'))s(o);new MutationObserver(o=>{for(const r of o)if(r.type==="childList")for(const i of r.addedNodes)i.tagName==="LINK"&&i.rel==="modulepreload"&&s(i)}).observe(document,{childList:!0,subtree:!0});function n(o){const r={};return o.integrity&&(r.integrity=o.integrity),o.referrerPolicy&&(r.referrerPolicy=o.referrerPolicy),o.crossOrigin==="use-credentials"?r.credentials="include":o.crossOrigin==="anonymous"?r.credentials="omit":r.credentials="same-origin",r}function s(o){if(o.ep)return;o.ep=!0;const r=n(o);fetch(o.href,r)}})();/**
* @vue/shared v3.5.16
* (c) 2018-present Yuxi (Evan) You and Vue contributors
* @license MIT
**//*! #__NO_SIDE_EFFECTS__ */function Mr(e){const t=Object.create(null);for(const n of e.split(","))t[n]=1;return n=>n in t}const _e={},pn=[],Tt=()=>{},Ua=()=>!1,As=e=>e.charCodeAt(0)===111&&e.charCodeAt(1)===110&&(e.charCodeAt(2)>122||e.charCodeAt(2)<97),Fr=e=>e.startsWith("onUpdate:"),je=Object.assign,Dr=(e,t)=>{const n=e.indexOf(t);n>-1&&e.splice(n,1)},Ha=Object.prototype.hasOwnProperty,fe=(e,t)=>Ha.call(e,t),J=Array.isArray,gn=e=>Qn(e)==="[object Map]",Rs=e=>Qn(e)==="[object Set]",ao=e=>Qn(e)==="[object Date]",Z=e=>typeof e=="function",Pe=e=>typeof e=="string",Lt=e=>typeof e=="symbol",ve=e=>e!==null&&typeof e=="object",Ui=e=>(ve(e)||Z(e))&&Z(e.then)&&Z(e.catch),Hi=Object.prototype.toString,Qn=e=>Hi.call(e),Va=e=>Qn(e).slice(8,-1),Vi=e=>Qn(e)==="[object Object]",xr=e=>Pe(e)&&e!=="NaN"&&e[0]!=="-"&&""+parseInt(e,10)===e,wn=Mr(",key,ref,ref_for,ref_key,onVnodeBeforeMount,onVnodeMounted,onVnodeBeforeUpdate,onVnodeUpdated,onVnodeBeforeUnmount,onVnodeUnmounted"),ks=e=>{const t=Object.create(null);return n=>t[n]||(t[n]=e(n))},Wa=/-(\w)/g,Bt=ks(e=>e.replace(Wa,(t,n)=>n?n.toUpperCase():"")),ja=/\B([A-Z])/g,an=ks(e=>e.replace(ja,"-$1").toLowerCase()),Wi=ks(e=>e.charAt(0).toUpperCase()+e.slice(1)),qs=ks(e=>e?`on${Wi(e)}`:""),Kt=(e,t)=>!Object.is(e,t),cs=(e,...t)=>{for(let n=0;n<e.length;n++)e[n](...t)},ji=(e,t,n,s=!1)=>{Object.defineProperty(e,t,{configurable:!0,enumerable:!1,writable:s,value:n})},ps=e=>{const t=parseFloat(e);return isNaN(t)?e:t};let co;const ws=()=>co||(co=typeof globalThis<"u"?globalThis:typeof self<"u"?self:typeof window<"u"?window:typeof global<"u"?global:{});function Zn(e){if(J(e)){const t={};for(let n=0;n<e.length;n++){const s=e[n],o=Pe(s)?Ya(s):Zn(s);if(o)for(const r in o)t[r]=o[r]}return t}else if(Pe(e)||ve(e))return e}const Ka=/;(?![^(]*\))/g,Ba=/:([^]+)/,Ga=/\/\*[^]*?\*\//g;function Ya(e){const t={};return e.replace(Ga,"").split(Ka).forEach(n=>{if(n){const s=n.split(Ba);s.length>1&&(t[s[0].trim()]=s[1].trim())}}),t}function Oe(e){let t="";if(Pe(e))t=e;else if(J(e))for(let n=0;n<e.length;n++){const s=Oe(e[n]);s&&(t+=s+" ")}else if(ve(e))for(const n in e)e[n]&&(t+=n+" ");return t.trim()}const za="itemscope,allowfullscreen,formnovalidate,ismap,nomodule,novalidate,readonly",Xa=Mr(za);function Ki(e){return!!e||e===""}function qa(e,t){if(e.length!==t.length)return!1;let n=!0;for(let s=0;n&&s<e.length;s++)n=Ms(e[s],t[s]);return n}function Ms(e,t){if(e===t)return!0;let n=ao(e),s=ao(t);if(n||s)return n&&s?e.getTime()===t.getTime():!1;if(n=Lt(e),s=Lt(t),n||s)return e===t;if(n=J(e),s=J(t),n||s)return n&&s?qa(e,t):!1;if(n=ve(e),s=ve(t),n||s){if(!n||!s)return!1;const o=Object.keys(e).length,r=Object.keys(t).length;if(o!==r)return!1;for(const i in e){const l=e.hasOwnProperty(i),a=t.hasOwnProperty(i);if(l&&!a||!l&&a||!Ms(e[i],t[i]))return!1}}return String(e)===String(t)}function Ja(e,t){return e.findIndex(n=>Ms(n,t))}const Bi=e=>!!(e&&e.__v_isRef===!0),X=e=>Pe(e)?e:e==null?"":J(e)||ve(e)&&(e.toString===Hi||!Z(e.toString))?Bi(e)?X(e.value):JSON.stringify(e,Gi,2):String(e),Gi=(e,t)=>Bi(t)?Gi(e,t.value):gn(t)?{[`Map(${t.size})`]:[...t.entries()].reduce((n,[s,o],r)=>(n[Js(s,r)+" =>"]=o,n),{})}:Rs(t)?{[`Set(${t.size})`]:[...t.values()].map(n=>Js(n))}:Lt(t)?Js(t):ve(t)&&!J(t)&&!Vi(t)?String(t):t,Js=(e,t="")=>{var n;return Lt(e)?`Symbol(${(n=e.description)!=null?n:t})`:e};/**
* @vue/reactivity v3.5.16
* (c) 2018-present Yuxi (Evan) You and Vue contributors
* @license MIT
**/let Qe;class Yi{constructor(t=!1){this.detached=t,this._active=!0,this._on=0,this.effects=[],this.cleanups=[],this._isPaused=!1,this.parent=Qe,!t&&Qe&&(this.index=(Qe.scopes||(Qe.scopes=[])).push(this)-1)}get active(){return this._active}pause(){if(this._active){this._isPaused=!0;let t,n;if(this.scopes)for(t=0,n=this.scopes.length;t<n;t++)this.scopes[t].pause();for(t=0,n=this.effects.length;t<n;t++)this.effects[t].pause()}}resume(){if(this._active&&this._isPaused){this._isPaused=!1;let t,n;if(this.scopes)for(t=0,n=this.scopes.length;t<n;t++)this.scopes[t].resume();for(t=0,n=this.effects.length;t<n;t++)this.effects[t].resume()}}run(t){if(this._active){const n=Qe;try{return Qe=this,t()}finally{Qe=n}}}on(){++this._on===1&&(this.prevScope=Qe,Qe=this)}off(){this._on>0&&--this._on===0&&(Qe=this.prevScope,this.prevScope=void 0)}stop(t){if(this._active){this._active=!1;let n,s;for(n=0,s=this.effects.length;n<s;n++)this.effects[n].stop();for(this.effects.length=0,n=0,s=this.cleanups.length;n<s;n++)this.cleanups[n]();if(this.cleanups.length=0,this.scopes){for(n=0,s=this.scopes.length;n<s;n++)this.scopes[n].stop(!0);this.scopes.length=0}if(!this.detached&&this.parent&&!t){const o=this.parent.scopes.pop();o&&o!==this&&(this.parent.scopes[this.index]=o,o.index=this.index)}this.parent=void 0}}}function zi(e){return new Yi(e)}function Qa(){return Qe}let pe;const Qs=new WeakSet;class Xi{constructor(t){this.fn=t,this.deps=void 0,this.depsTail=void 0,this.flags=5,this.next=void 0,this.cleanup=void 0,this.scheduler=void 0,Qe&&Qe.active&&Qe.effects.push(this)}pause(){this.flags|=64}resume(){this.flags&64&&(this.flags&=-65,Qs.has(this)&&(Qs.delete(this),this.trigger()))}notify(){this.flags&2&&!(this.flags&32)||this.flags&8||Ji(this)}run(){if(!(this.flags&1))return this.fn();this.flags|=2,uo(this),Qi(this);const t=pe,n=mt;pe=this,mt=!0;try{return this.fn()}finally{Zi(this),pe=t,mt=n,this.flags&=-3}}stop(){if(this.flags&1){for(let t=this.deps;t;t=t.nextDep)Hr(t);this.deps=this.depsTail=void 0,uo(this),this.onStop&&this.onStop(),this.flags&=-2}}trigger(){this.flags&64?Qs.add(this):this.scheduler?this.scheduler():this.runIfDirty()}runIfDirty(){dr(this)&&this.run()}get dirty(){return dr(this)}}let qi=0,Mn,Fn;function Ji(e,t=!1){if(e.flags|=8,t){e.next=Fn,Fn=e;return}e.next=Mn,Mn=e}function $r(){qi++}function Ur(){if(--qi>0)return;if(Fn){let t=Fn;for(Fn=void 0;t;){const n=t.next;t.next=void 0,t.flags&=-9,t=n}}let e;for(;Mn;){let t=Mn;for(Mn=void 0;t;){const n=t.next;if(t.next=void 0,t.flags&=-9,t.flags&1)try{t.trigger()}catch(s){e||(e=s)}t=n}}if(e)throw e}function Qi(e){for(let t=e.deps;t;t=t.nextDep)t.version=-1,t.prevActiveLink=t.dep.activeLink,t.dep.activeLink=t}function Zi(e){let t,n=e.depsTail,s=n;for(;s;){const o=s.prevDep;s.version===-1?(s===n&&(n=o),Hr(s),Za(s)):t=s,s.dep.activeLink=s.prevActiveLink,s.prevActiveLink=void 0,s=o}e.deps=t,e.depsTail=n}function dr(e){for(let t=e.deps;t;t=t.nextDep)if(t.dep.version!==t.version||t.dep.computed&&(el(t.dep.computed)||t.dep.version!==t.version))return!0;return!!e._dirty}function el(e){if(e.flags&4&&!(e.flags&16)||(e.flags&=-17,e.globalVersion===Vn)||(e.globalVersion=Vn,!e.isSSR&&e.flags&128&&(!e.deps&&!e._dirty||!dr(e))))return;e.flags|=2;const t=e.dep,n=pe,s=mt;pe=e,mt=!0;try{Qi(e);const o=e.fn(e._value);(t.version===0||Kt(o,e._value))&&(e.flags|=128,e._value=o,t.version++)}catch(o){throw t.version++,o}finally{pe=n,mt=s,Zi(e),e.flags&=-3}}function Hr(e,t=!1){const{dep:n,prevSub:s,nextSub:o}=e;if(s&&(s.nextSub=o,e.prevSub=void 0),o&&(o.prevSub=s,e.nextSub=void 0),n.subs===e&&(n.subs=s,!s&&n.computed)){n.computed.flags&=-5;for(let r=n.computed.deps;r;r=r.nextDep)Hr(r,!0)}!t&&!--n.sc&&n.map&&n.map.delete(n.key)}function Za(e){const{prevDep:t,nextDep:n}=e;t&&(t.nextDep=n,e.prevDep=void 0),n&&(n.prevDep=t,e.nextDep=void 0)}let mt=!0;const tl=[];function Ft(){tl.push(mt),mt=!1}function Dt(){const e=tl.pop();mt=e===void 0?!0:e}function uo(e){const{cleanup:t}=e;if(e.cleanup=void 0,t){const n=pe;pe=void 0;try{t()}finally{pe=n}}}let Vn=0;class ec{constructor(t,n){this.sub=t,this.dep=n,this.version=n.version,this.nextDep=this.prevDep=this.nextSub=this.prevSub=this.prevActiveLink=void 0}}class Vr{constructor(t){this.computed=t,this.version=0,this.activeLink=void 0,this.subs=void 0,this.map=void 0,this.key=void 0,this.sc=0}track(t){if(!pe||!mt||pe===this.computed)return;let n=this.activeLink;if(n===void 0||n.sub!==pe)n=this.activeLink=new ec(pe,this),pe.deps?(n.prevDep=pe.depsTail,pe.depsTail.nextDep=n,pe.depsTail=n):pe.deps=pe.depsTail=n,nl(n);else if(n.version===-1&&(n.version=this.version,n.nextDep)){const s=n.nextDep;s.prevDep=n.prevDep,n.prevDep&&(n.prevDep.nextDep=s),n.prevDep=pe.depsTail,n.nextDep=void 0,pe.depsTail.nextDep=n,pe.depsTail=n,pe.deps===n&&(pe.deps=s)}return n}trigger(t){this.version++,Vn++,this.notify(t)}notify(t){$r();try{for(let n=this.subs;n;n=n.prevSub)n.sub.notify()&&n.sub.dep.notify()}finally{Ur()}}}function nl(e){if(e.dep.sc++,e.sub.flags&4){const t=e.dep.computed;if(t&&!e.dep.subs){t.flags|=20;for(let s=t.deps;s;s=s.nextDep)nl(s)}const n=e.dep.subs;n!==e&&(e.prevSub=n,n&&(n.nextSub=e)),e.dep.subs=e}}const hr=new WeakMap,on=Symbol(""),mr=Symbol(""),Wn=Symbol("");function He(e,t,n){if(mt&&pe){let s=hr.get(e);s||hr.set(e,s=new Map);let o=s.get(n);o||(s.set(n,o=new Vr),o.map=s,o.key=n),o.track()}}function kt(e,t,n,s,o,r){const i=hr.get(e);if(!i){Vn++;return}const l=a=>{a&&a.trigger()};if($r(),t==="clear")i.forEach(l);else{const a=J(e),c=a&&xr(n);if(a&&n==="length"){const u=Number(s);i.forEach((f,m)=>{(m==="length"||m===Wn||!Lt(m)&&m>=u)&&l(f)})}else switch((n!==void 0||i.has(void 0))&&l(i.get(n)),c&&l(i.get(Wn)),t){case"add":a?c&&l(i.get("length")):(l(i.get(on)),gn(e)&&l(i.get(mr)));break;case"delete":a||(l(i.get(on)),gn(e)&&l(i.get(mr)));break;case"set":gn(e)&&l(i.get(on));break}}Ur()}function fn(e){const t=ue(e);return t===e?t:(He(t,"iterate",Wn),ct(e)?t:t.map(Me))}function Fs(e){return He(e=ue(e),"iterate",Wn),e}const tc={__proto__:null,[Symbol.iterator](){return Zs(this,Symbol.iterator,Me)},concat(...e){return fn(this).concat(...e.map(t=>J(t)?fn(t):t))},entries(){return Zs(this,"entries",e=>(e[1]=Me(e[1]),e))},every(e,t){return Ot(this,"every",e,t,void 0,arguments)},filter(e,t){return Ot(this,"filter",e,t,n=>n.map(Me),arguments)},find(e,t){return Ot(this,"find",e,t,Me,arguments)},findIndex(e,t){return Ot(this,"findIndex",e,t,void 0,arguments)},findLast(e,t){return Ot(this,"findLast",e,t,Me,arguments)},findLastIndex(e,t){return Ot(this,"findLastIndex",e,t,void 0,arguments)},forEach(e,t){return Ot(this,"forEach",e,t,void 0,arguments)},includes(...e){return er(this,"includes",e)},indexOf(...e){return er(this,"indexOf",e)},join(e){return fn(this).join(e)},lastIndexOf(...e){return er(this,"lastIndexOf",e)},map(e,t){return Ot(this,"map",e,t,void 0,arguments)},pop(){return Pn(this,"pop")},push(...e){return Pn(this,"push",e)},reduce(e,...t){return fo(this,"reduce",e,t)},reduceRight(e,...t){return fo(this,"reduceRight",e,t)},shift(){return Pn(this,"shift")},some(e,t){return Ot(this,"some",e,t,void 0,arguments)},splice(...e){return Pn(this,"splice",e)},toReversed(){return fn(this).toReversed()},toSorted(e){return fn(this).toSorted(e)},toSpliced(...e){return fn(this).toSpliced(...e)},unshift(...e){return Pn(this,"unshift",e)},values(){return Zs(this,"values",Me)}};function Zs(e,t,n){const s=Fs(e),o=s[t]();return s!==e&&!ct(e)&&(o._next=o.next,o.next=()=>{const r=o._next();return r.value&&(r.value=n(r.value)),r}),o}const nc=Array.prototype;function Ot(e,t,n,s,o,r){const i=Fs(e),l=i!==e&&!ct(e),a=i[t];if(a!==nc[t]){const f=a.apply(e,r);return l?Me(f):f}let c=n;i!==e&&(l?c=function(f,m){return n.call(this,Me(f),m,e)}:n.length>2&&(c=function(f,m){return n.call(this,f,m,e)}));const u=a.call(i,c,s);return l&&o?o(u):u}function fo(e,t,n,s){const o=Fs(e);let r=n;return o!==e&&(ct(e)?n.length>3&&(r=function(i,l,a){return n.call(this,i,l,a,e)}):r=function(i,l,a){return n.call(this,i,Me(l),a,e)}),o[t](r,...s)}function er(e,t,n){const s=ue(e);He(s,"iterate",Wn);const o=s[t](...n);return(o===-1||o===!1)&&Kr(n[0])?(n[0]=ue(n[0]),s[t](...n)):o}function Pn(e,t,n=[]){Ft(),$r();const s=ue(e)[t].apply(e,n);return Ur(),Dt(),s}const sc=Mr("__proto__,__v_isRef,__isVue"),sl=new Set(Object.getOwnPropertyNames(Symbol).filter(e=>e!=="arguments"&&e!=="caller").map(e=>Symbol[e]).filter(Lt));function rc(e){Lt(e)||(e=String(e));const t=ue(this);return He(t,"has",e),t.hasOwnProperty(e)}class rl{constructor(t=!1,n=!1){this._isReadonly=t,this._isShallow=n}get(t,n,s){if(n==="__v_skip")return t.__v_skip;const o=this._isReadonly,r=this._isShallow;if(n==="__v_isReactive")return!o;if(n==="__v_isReadonly")return o;if(n==="__v_isShallow")return r;if(n==="__v_raw")return s===(o?r?mc:al:r?ll:il).get(t)||Object.getPrototypeOf(t)===Object.getPrototypeOf(s)?t:void 0;const i=J(t);if(!o){let a;if(i&&(a=tc[n]))return a;if(n==="hasOwnProperty")return rc}const l=Reflect.get(t,n,Fe(t)?t:s);return(Lt(n)?sl.has(n):sc(n))||(o||He(t,"get",n),r)?l:Fe(l)?i&&xr(n)?l:l.value:ve(l)?o?ul(l):Ds(l):l}}class ol extends rl{constructor(t=!1){super(!1,t)}set(t,n,s,o){let r=t[n];if(!this._isShallow){const a=Gt(r);if(!ct(s)&&!Gt(s)&&(r=ue(r),s=ue(s)),!J(t)&&Fe(r)&&!Fe(s))return a?!1:(r.value=s,!0)}const i=J(t)&&xr(n)?Number(n)<t.length:fe(t,n),l=Reflect.set(t,n,s,Fe(t)?t:o);return t===ue(o)&&(i?Kt(s,r)&&kt(t,"set",n,s):kt(t,"add",n,s)),l}deleteProperty(t,n){const s=fe(t,n);t[n];const o=Reflect.deleteProperty(t,n);return o&&s&&kt(t,"delete",n,void 0),o}has(t,n){const s=Reflect.has(t,n);return(!Lt(n)||!sl.has(n))&&He(t,"has",n),s}ownKeys(t){return He(t,"iterate",J(t)?"length":on),Reflect.ownKeys(t)}}class oc extends rl{constructor(t=!1){super(!0,t)}set(t,n){return!0}deleteProperty(t,n){return!0}}const ic=new ol,lc=new oc,ac=new ol(!0);const _r=e=>e,ss=e=>Reflect.getPrototypeOf(e);function cc(e,t,n){return function(...s){const o=this.__v_raw,r=ue(o),i=gn(r),l=e==="entries"||e===Symbol.iterator&&i,a=e==="keys"&&i,c=o[e](...s),u=n?_r:t?gs:Me;return!t&&He(r,"iterate",a?mr:on),{next(){const{value:f,done:m}=c.next();return m?{value:f,done:m}:{value:l?[u(f[0]),u(f[1])]:u(f),done:m}},[Symbol.iterator](){return this}}}}function rs(e){return function(...t){return e==="delete"?!1:e==="clear"?void 0:this}}function uc(e,t){const n={get(o){const r=this.__v_raw,i=ue(r),l=ue(o);e||(Kt(o,l)&&He(i,"get",o),He(i,"get",l));const{has:a}=ss(i),c=t?_r:e?gs:Me;if(a.call(i,o))return c(r.get(o));if(a.call(i,l))return c(r.get(l));r!==i&&r.get(o)},get size(){const o=this.__v_raw;return!e&&He(ue(o),"iterate",on),Reflect.get(o,"size",o)},has(o){const r=this.__v_raw,i=ue(r),l=ue(o);return e||(Kt(o,l)&&He(i,"has",o),He(i,"has",l)),o===l?r.has(o):r.has(o)||r.has(l)},forEach(o,r){const i=this,l=i.__v_raw,a=ue(l),c=t?_r:e?gs:Me;return!e&&He(a,"iterate",on),l.forEach((u,f)=>o.call(r,c(u),c(f),i))}};return je(n,e?{add:rs("add"),set:rs("set"),delete:rs("delete"),clear:rs("clear")}:{add(o){!t&&!ct(o)&&!Gt(o)&&(o=ue(o));const r=ue(this);return ss(r).has.call(r,o)||(r.add(o),kt(r,"add",o,o)),this},set(o,r){!t&&!ct(r)&&!Gt(r)&&(r=ue(r));const i=ue(this),{has:l,get:a}=ss(i);let c=l.call(i,o);c||(o=ue(o),c=l.call(i,o));const u=a.call(i,o);return i.set(o,r),c?Kt(r,u)&&kt(i,"set",o,r):kt(i,"add",o,r),this},delete(o){const r=ue(this),{has:i,get:l}=ss(r);let a=i.call(r,o);a||(o=ue(o),a=i.call(r,o)),l&&l.call(r,o);const c=r.delete(o);return a&&kt(r,"delete",o,void 0),c},clear(){const o=ue(this),r=o.size!==0,i=o.clear();return r&&kt(o,"clear",void 0,void 0),i}}),["keys","values","entries",Symbol.iterator].forEach(o=>{n[o]=cc(o,e,t)}),n}function Wr(e,t){const n=uc(e,t);return(s,o,r)=>o==="__v_isReactive"?!e:o==="__v_isReadonly"?e:o==="__v_raw"?s:Reflect.get(fe(n,o)&&o in s?n:s,o,r)}const fc={get:Wr(!1,!1)},dc={get:Wr(!1,!0)},hc={get:Wr(!0,!1)};const il=new WeakMap,ll=new WeakMap,al=new WeakMap,mc=new WeakMap;function _c(e){switch(e){case"Object":case"Array":return 1;case"Map":case"Set":case"WeakMap":case"WeakSet":return 2;default:return 0}}function pc(e){return e.__v_skip||!Object.isExtensible(e)?0:_c(Va(e))}function Ds(e){return Gt(e)?e:jr(e,!1,ic,fc,il)}function cl(e){return jr(e,!1,ac,dc,ll)}function ul(e){return jr(e,!0,lc,hc,al)}function jr(e,t,n,s,o){if(!ve(e)||e.__v_raw&&!(t&&e.__v_isReactive))return e;const r=pc(e);if(r===0)return e;const i=o.get(e);if(i)return i;const l=new Proxy(e,r===2?s:n);return o.set(e,l),l}function vn(e){return Gt(e)?vn(e.__v_raw):!!(e&&e.__v_isReactive)}function Gt(e){return!!(e&&e.__v_isReadonly)}function ct(e){return!!(e&&e.__v_isShallow)}function Kr(e){return e?!!e.__v_raw:!1}function ue(e){const t=e&&e.__v_raw;return t?ue(t):e}function fl(e){return!fe(e,"__v_skip")&&Object.isExtensible(e)&&ji(e,"__v_skip",!0),e}const Me=e=>ve(e)?Ds(e):e,gs=e=>ve(e)?ul(e):e;function Fe(e){return e?e.__v_isRef===!0:!1}function Ve(e){return dl(e,!1)}function Br(e){return dl(e,!0)}function dl(e,t){return Fe(e)?e:new gc(e,t)}class gc{constructor(t,n){this.dep=new Vr,this.__v_isRef=!0,this.__v_isShallow=!1,this._rawValue=n?t:ue(t),this._value=n?t:Me(t),this.__v_isShallow=n}get value(){return this.dep.track(),this._value}set value(t){const n=this._rawValue,s=this.__v_isShallow||ct(t)||Gt(t);t=s?t:ue(t),Kt(t,n)&&(this._rawValue=t,this._value=s?t:Me(t),this.dep.trigger())}}function Le(e){return Fe(e)?e.value:e}const vc={get:(e,t,n)=>t==="__v_raw"?e:Le(Reflect.get(e,t,n)),set:(e,t,n,s)=>{const o=e[t];return Fe(o)&&!Fe(n)?(o.value=n,!0):Reflect.set(e,t,n,s)}};function hl(e){return vn(e)?e:new Proxy(e,vc)}class bc{constructor(t,n,s){this.fn=t,this.setter=n,this._value=void 0,this.dep=new Vr(this),this.__v_isRef=!0,this.deps=void 0,this.depsTail=void 0,this.flags=16,this.globalVersion=Vn-1,this.next=void 0,this.effect=this,this.__v_isReadonly=!n,this.isSSR=s}notify(){if(this.flags|=16,!(this.flags&8)&&pe!==this)return Ji(this,!0),!0}get value(){const t=this.dep.track();return el(this),t&&(t.version=this.dep.version),this._value}set value(t){this.setter&&this.setter(t)}}function yc(e,t,n=!1){let s,o;return Z(e)?s=e:(s=e.get,o=e.set),new bc(s,o,n)}const os={},vs=new WeakMap;let sn;function Ec(e,t=!1,n=sn){if(n){let s=vs.get(n);s||vs.set(n,s=[]),s.push(e)}}function Tc(e,t,n=_e){const{immediate:s,deep:o,once:r,scheduler:i,augmentJob:l,call:a}=n,c=y=>o?y:ct(y)||o===!1||o===0?wt(y,1):wt(y);let u,f,m,v,O=!1,C=!1;if(Fe(e)?(f=()=>e.value,O=ct(e)):vn(e)?(f=()=>c(e),O=!0):J(e)?(C=!0,O=e.some(y=>vn(y)||ct(y)),f=()=>e.map(y=>{if(Fe(y))return y.value;if(vn(y))return c(y);if(Z(y))return a?a(y,2):y()})):Z(e)?t?f=a?()=>a(e,2):e:f=()=>{if(m){Ft();try{m()}finally{Dt()}}const y=sn;sn=u;try{return a?a(e,3,[v]):e(v)}finally{sn=y}}:f=Tt,t&&o){const y=f,R=o===!0?1/0:o;f=()=>wt(y(),R)}const D=Qa(),E=()=>{u.stop(),D&&D.active&&Dr(D.effects,u)};if(r&&t){const y=t;t=(...R)=>{y(...R),E()}}let F=C?new Array(e.length).fill(os):os;const b=y=>{if(!(!(u.flags&1)||!u.dirty&&!y))if(t){const R=u.run();if(o||O||(C?R.some((A,x)=>Kt(A,F[x])):Kt(R,F))){m&&m();const A=sn;sn=u;try{const x=[R,F===os?void 0:C&&F[0]===os?[]:F,v];F=R,a?a(t,3,x):t(...x)}finally{sn=A}}}else u.run()};return l&&l(b),u=new Xi(f),u.scheduler=i?()=>i(b,!1):b,v=y=>Ec(y,!1,u),m=u.onStop=()=>{const y=vs.get(u);if(y){if(a)a(y,4);else for(const R of y)R();vs.delete(u)}},t?s?b(!0):F=u.run():i?i(b.bind(null,!0),!0):u.run(),E.pause=u.pause.bind(u),E.resume=u.resume.bind(u),E.stop=E,E}function wt(e,t=1/0,n){if(t<=0||!ve(e)||e.__v_skip||(n=n||new Set,n.has(e)))return e;if(n.add(e),t--,Fe(e))wt(e.value,t,n);else if(J(e))for(let s=0;s<e.length;s++)wt(e[s],t,n);else if(Rs(e)||gn(e))e.forEach(s=>{wt(s,t,n)});else if(Vi(e)){for(const s in e)wt(e[s],t,n);for(const s of Object.getOwnPropertySymbols(e))Object.prototype.propertyIsEnumerable.call(e,s)&&wt(e[s],t,n)}return e}/**
* @vue/runtime-core v3.5.16
* (c) 2018-present Yuxi (Evan) You and Vue contributors
* @license MIT
**/function es(e,t,n,s){try{return s?e(...s):e()}catch(o){xs(o,t,n)}}function Ct(e,t,n,s){if(Z(e)){const o=es(e,t,n,s);return o&&Ui(o)&&o.catch(r=>{xs(r,t,n)}),o}if(J(e)){const o=[];for(let r=0;r<e.length;r++)o.push(Ct(e[r],t,n,s));return o}}function xs(e,t,n,s=!0){const o=t?t.vnode:null,{errorHandler:r,throwUnhandledErrorInProduction:i}=t&&t.appContext.config||_e;if(t){let l=t.parent;const a=t.proxy,c=`https://vuejs.org/error-reference/#runtime-${n}`;for(;l;){const u=l.ec;if(u){for(let f=0;f<u.length;f++)if(u[f](e,a,c)===!1)return}l=l.parent}if(r){Ft(),es(r,null,10,[e,a,c]),Dt();return}}Ic(e,n,o,s,i)}function Ic(e,t,n,s=!0,o=!1){if(o)throw e;console.error(e)}const ze=[];let bt=-1;const bn=[];let Vt=null,dn=0;const ml=Promise.resolve();let bs=null;function Gr(e){const t=bs||ml;return e?t.then(this?e.bind(this):e):t}function Sc(e){let t=bt+1,n=ze.length;for(;t<n;){const s=t+n>>>1,o=ze[s],r=jn(o);r<e||r===e&&o.flags&2?t=s+1:n=s}return t}function Yr(e){if(!(e.flags&1)){const t=jn(e),n=ze[ze.length-1];!n||!(e.flags&2)&&t>=jn(n)?ze.push(e):ze.splice(Sc(t),0,e),e.flags|=1,_l()}}function _l(){bs||(bs=ml.then(gl))}function Lc(e){J(e)?bn.push(...e):Vt&&e.id===-1?Vt.splice(dn+1,0,e):e.flags&1||(bn.push(e),e.flags|=1),_l()}function ho(e,t,n=bt+1){for(;n<ze.length;n++){const s=ze[n];if(s&&s.flags&2){if(e&&s.id!==e.uid)continue;ze.splice(n,1),n--,s.flags&4&&(s.flags&=-2),s(),s.flags&4||(s.flags&=-2)}}}function pl(e){if(bn.length){const t=[...new Set(bn)].sort((n,s)=>jn(n)-jn(s));if(bn.length=0,Vt){Vt.push(...t);return}for(Vt=t,dn=0;dn<Vt.length;dn++){const n=Vt[dn];n.flags&4&&(n.flags&=-2),n.flags&8||n(),n.flags&=-2}Vt=null,dn=0}}const jn=e=>e.id==null?e.flags&2?-1:1/0:e.id;function gl(e){try{for(bt=0;bt<ze.length;bt++){const t=ze[bt];t&&!(t.flags&8)&&(t.flags&4&&(t.flags&=-2),es(t,t.i,t.i?15:14),t.flags&4||(t.flags&=-2))}}finally{for(;bt<ze.length;bt++){const t=ze[bt];t&&(t.flags&=-2)}bt=-1,ze.length=0,pl(),bs=null,(ze.length||bn.length)&&gl()}}let st=null,vl=null;function ys(e){const t=st;return st=e,vl=e&&e.type.__scopeId||null,t}function xe(e,t=st,n){if(!t||e._n)return e;const s=(...o)=>{s._d&&To(-1);const r=ys(t);let i;try{i=e(...o)}finally{ys(r),s._d&&To(1)}return i};return s._n=!0,s._c=!0,s._d=!0,s}function jp(e,t){if(st===null)return e;const n=Hs(st),s=e.dirs||(e.dirs=[]);for(let o=0;o<t.length;o++){let[r,i,l,a=_e]=t[o];r&&(Z(r)&&(r={mounted:r,updated:r}),r.deep&&wt(i),s.push({dir:r,instance:n,value:i,oldValue:void 0,arg:l,modifiers:a}))}return e}function Zt(e,t,n,s){const o=e.dirs,r=t&&t.dirs;for(let i=0;i<o.length;i++){const l=o[i];r&&(l.oldValue=r[i].value);let a=l.dir[s];a&&(Ft(),Ct(a,n,8,[e.el,l,e,t]),Dt())}}const Cc=Symbol("_vte"),Oc=e=>e.__isTeleport;function zr(e,t){e.shapeFlag&6&&e.component?(e.transition=t,zr(e.component.subTree,t)):e.shapeFlag&128?(e.ssContent.transition=t.clone(e.ssContent),e.ssFallback.transition=t.clone(e.ssFallback)):e.transition=t}/*! #__NO_SIDE_EFFECTS__ */function Ze(e,t){return Z(e)?je({name:e.name},t,{setup:e}):e}function bl(e){e.ids=[e.ids[0]+e.ids[2]+++"-",0,0]}function Es(e,t,n,s,o=!1){if(J(e)){e.forEach((O,C)=>Es(O,t&&(J(t)?t[C]:t),n,s,o));return}if(Dn(s)&&!o){s.shapeFlag&512&&s.type.__asyncResolved&&s.component.subTree.component&&Es(e,t,n,s.component.subTree);return}const r=s.shapeFlag&4?Hs(s.component):s.el,i=o?null:r,{i:l,r:a}=e,c=t&&t.r,u=l.refs===_e?l.refs={}:l.refs,f=l.setupState,m=ue(f),v=f===_e?()=>!1:O=>fe(m,O);if(c!=null&&c!==a&&(Pe(c)?(u[c]=null,v(c)&&(f[c]=null)):Fe(c)&&(c.value=null)),Z(a))es(a,l,12,[i,u]);else{const O=Pe(a),C=Fe(a);if(O||C){const D=()=>{if(e.f){const E=O?v(a)?f[a]:u[a]:a.value;o?J(E)&&Dr(E,r):J(E)?E.includes(r)||E.push(r):O?(u[a]=[r],v(a)&&(f[a]=u[a])):(a.value=[r],e.k&&(u[e.k]=a.value))}else O?(u[a]=i,v(a)&&(f[a]=i)):C&&(a.value=i,e.k&&(u[e.k]=i))};i?(D.id=-1,nt(D,n)):D()}}}ws().requestIdleCallback;ws().cancelIdleCallback;const Dn=e=>!!e.type.__asyncLoader,yl=e=>e.type.__isKeepAlive;function Pc(e,t){El(e,"a",t)}function Nc(e,t){El(e,"da",t)}function El(e,t,n=We){const s=e.__wdc||(e.__wdc=()=>{let o=n;for(;o;){if(o.isDeactivated)return;o=o.parent}return e()});if($s(t,s,n),n){let o=n.parent;for(;o&&o.parent;)yl(o.parent.vnode)&&Ac(s,t,n,o),o=o.parent}}function Ac(e,t,n,s){const o=$s(t,e,s,!0);un(()=>{Dr(s[t],o)},n)}function $s(e,t,n=We,s=!1){if(n){const o=n[e]||(n[e]=[]),r=t.__weh||(t.__weh=(...i)=>{Ft();const l=ns(n),a=Ct(t,n,e,i);return l(),Dt(),a});return s?o.unshift(r):o.push(r),r}}const xt=e=>(t,n=We)=>{(!Gn||e==="sp")&&$s(e,(...s)=>t(...s),n)},Tl=xt("bm"),cn=xt("m"),Rc=xt("bu"),kc=xt("u"),wc=xt("bum"),un=xt("um"),Mc=xt("sp"),Fc=xt("rtg"),Dc=xt("rtc");function xc(e,t=We){$s("ec",e,t)}const $c=Symbol.for("v-ndc");function us(e,t,n,s){let o;const r=n,i=J(e);if(i||Pe(e)){const l=i&&vn(e);let a=!1,c=!1;l&&(a=!ct(e),c=Gt(e),e=Fs(e)),o=new Array(e.length);for(let u=0,f=e.length;u<f;u++)o[u]=t(a?c?gs(Me(e[u])):Me(e[u]):e[u],u,void 0,r)}else if(typeof e=="number"){o=new Array(e);for(let l=0;l<e;l++)o[l]=t(l+1,l,void 0,r)}else if(ve(e))if(e[Symbol.iterator])o=Array.from(e,(l,a)=>t(l,a,void 0,r));else{const l=Object.keys(e);o=new Array(l.length);for(let a=0,c=l.length;a<c;a++){const u=l[a];o[a]=t(e[u],u,a,r)}}else o=[];return o}const pr=e=>e?Wl(e)?Hs(e):pr(e.parent):null,xn=je(Object.create(null),{$:e=>e,$el:e=>e.vnode.el,$data:e=>e.data,$props:e=>e.props,$attrs:e=>e.attrs,$slots:e=>e.slots,$refs:e=>e.refs,$parent:e=>pr(e.parent),$root:e=>pr(e.root),$host:e=>e.ce,$emit:e=>e.emit,$options:e=>Sl(e),$forceUpdate:e=>e.f||(e.f=()=>{Yr(e.update)}),$nextTick:e=>e.n||(e.n=Gr.bind(e.proxy)),$watch:e=>ou.bind(e)}),tr=(e,t)=>e!==_e&&!e.__isScriptSetup&&fe(e,t),Uc={get({_:e},t){if(t==="__v_skip")return!0;const{ctx:n,setupState:s,data:o,props:r,accessCache:i,type:l,appContext:a}=e;let c;if(t[0]!=="$"){const v=i[t];if(v!==void 0)switch(v){case 1:return s[t];case 2:return o[t];case 4:return n[t];case 3:return r[t]}else{if(tr(s,t))return i[t]=1,s[t];if(o!==_e&&fe(o,t))return i[t]=2,o[t];if((c=e.propsOptions[0])&&fe(c,t))return i[t]=3,r[t];if(n!==_e&&fe(n,t))return i[t]=4,n[t];gr&&(i[t]=0)}}const u=xn[t];let f,m;if(u)return t==="$attrs"&&He(e.attrs,"get",""),u(e);if((f=l.__cssModules)&&(f=f[t]))return f;if(n!==_e&&fe(n,t))return i[t]=4,n[t];if(m=a.config.globalProperties,fe(m,t))return m[t]},set({_:e},t,n){const{data:s,setupState:o,ctx:r}=e;return tr(o,t)?(o[t]=n,!0):s!==_e&&fe(s,t)?(s[t]=n,!0):fe(e.props,t)||t[0]==="$"&&t.slice(1)in e?!1:(r[t]=n,!0)},has({_:{data:e,setupState:t,accessCache:n,ctx:s,appContext:o,propsOptions:r}},i){let l;return!!n[i]||e!==_e&&fe(e,i)||tr(t,i)||(l=r[0])&&fe(l,i)||fe(s,i)||fe(xn,i)||fe(o.config.globalProperties,i)},defineProperty(e,t,n){return n.get!=null?e._.accessCache[t]=0:fe(n,"value")&&this.set(e,t,n.value,null),Reflect.defineProperty(e,t,n)}};function mo(e){return J(e)?e.reduce((t,n)=>(t[n]=null,t),{}):e}let gr=!0;function Hc(e){const t=Sl(e),n=e.proxy,s=e.ctx;gr=!1,t.beforeCreate&&_o(t.beforeCreate,e,"bc");const{data:o,computed:r,methods:i,watch:l,provide:a,inject:c,created:u,beforeMount:f,mounted:m,beforeUpdate:v,updated:O,activated:C,deactivated:D,beforeDestroy:E,beforeUnmount:F,destroyed:b,unmounted:y,render:R,renderTracked:A,renderTriggered:x,errorCaptured:B,serverPrefetch:H,expose:ae,inheritAttrs:Te,components:ee,directives:me,filters:ut}=t;if(c&&Vc(c,s,null),i)for(const oe in i){const se=i[oe];Z(se)&&(s[oe]=se.bind(n))}if(o){const oe=o.call(n,n);ve(oe)&&(e.data=Ds(oe))}if(gr=!0,r)for(const oe in r){const se=r[oe],Ke=Z(se)?se.bind(n,n):Z(se.get)?se.get.bind(n,n):Tt,qe=!Z(se)&&Z(se.set)?se.set.bind(n):Tt,Ie=Ee({get:Ke,set:qe});Object.defineProperty(s,oe,{enumerable:!0,configurable:!0,get:()=>Ie.value,set:Se=>Ie.value=Se})}if(l)for(const oe in l)Il(l[oe],s,n,oe);if(a){const oe=Z(a)?a.call(n):a;Reflect.ownKeys(oe).forEach(se=>{fs(se,oe[se])})}u&&_o(u,e,"c");function de(oe,se){J(se)?se.forEach(Ke=>oe(Ke.bind(n))):se&&oe(se.bind(n))}if(de(Tl,f),de(cn,m),de(Rc,v),de(kc,O),de(Pc,C),de(Nc,D),de(xc,B),de(Dc,A),de(Fc,x),de(wc,F),de(un,y),de(Mc,H),J(ae))if(ae.length){const oe=e.exposed||(e.exposed={});ae.forEach(se=>{Object.defineProperty(oe,se,{get:()=>n[se],set:Ke=>n[se]=Ke})})}else e.exposed||(e.exposed={});R&&e.render===Tt&&(e.render=R),Te!=null&&(e.inheritAttrs=Te),ee&&(e.components=ee),me&&(e.directives=me),H&&bl(e)}function Vc(e,t,n=Tt){J(e)&&(e=vr(e));for(const s in e){const o=e[s];let r;ve(o)?"default"in o?r=It(o.from||s,o.default,!0):r=It(o.from||s):r=It(o),Fe(r)?Object.defineProperty(t,s,{enumerable:!0,configurable:!0,get:()=>r.value,set:i=>r.value=i}):t[s]=r}}function _o(e,t,n){Ct(J(e)?e.map(s=>s.bind(t.proxy)):e.bind(t.proxy),t,n)}function Il(e,t,n,s){let o=s.includes(".")?xl(n,s):()=>n[s];if(Pe(e)){const r=t[e];Z(r)&&ln(o,r)}else if(Z(e))ln(o,e.bind(n));else if(ve(e))if(J(e))e.forEach(r=>Il(r,t,n,s));else{const r=Z(e.handler)?e.handler.bind(n):t[e.handler];Z(r)&&ln(o,r,e)}}function Sl(e){const t=e.type,{mixins:n,extends:s}=t,{mixins:o,optionsCache:r,config:{optionMergeStrategies:i}}=e.appContext,l=r.get(t);let a;return l?a=l:!o.length&&!n&&!s?a=t:(a={},o.length&&o.forEach(c=>Ts(a,c,i,!0)),Ts(a,t,i)),ve(t)&&r.set(t,a),a}function Ts(e,t,n,s=!1){const{mixins:o,extends:r}=t;r&&Ts(e,r,n,!0),o&&o.forEach(i=>Ts(e,i,n,!0));for(const i in t)if(!(s&&i==="expose")){const l=Wc[i]||n&&n[i];e[i]=l?l(e[i],t[i]):t[i]}return e}const Wc={data:po,props:go,emits:go,methods:kn,computed:kn,beforeCreate:Ge,created:Ge,beforeMount:Ge,mounted:Ge,beforeUpdate:Ge,updated:Ge,beforeDestroy:Ge,beforeUnmount:Ge,destroyed:Ge,unmounted:Ge,activated:Ge,deactivated:Ge,errorCaptured:Ge,serverPrefetch:Ge,components:kn,directives:kn,watch:Kc,provide:po,inject:jc};function po(e,t){return t?e?function(){return je(Z(e)?e.call(this,this):e,Z(t)?t.call(this,this):t)}:t:e}function jc(e,t){return kn(vr(e),vr(t))}function vr(e){if(J(e)){const t={};for(let n=0;n<e.length;n++)t[e[n]]=e[n];return t}return e}function Ge(e,t){return e?[...new Set([].concat(e,t))]:t}function kn(e,t){return e?je(Object.create(null),e,t):t}function go(e,t){return e?J(e)&&J(t)?[...new Set([...e,...t])]:je(Object.create(null),mo(e),mo(t??{})):t}function Kc(e,t){if(!e)return t;if(!t)return e;const n=je(Object.create(null),e);for(const s in t)n[s]=Ge(e[s],t[s]);return n}function Ll(){return{app:null,config:{isNativeTag:Ua,performance:!1,globalProperties:{},optionMergeStrategies:{},errorHandler:void 0,warnHandler:void 0,compilerOptions:{}},mixins:[],components:{},directives:{},provides:Object.create(null),optionsCache:new WeakMap,propsCache:new WeakMap,emitsCache:new WeakMap}}let Bc=0;function Gc(e,t){return function(s,o=null){Z(s)||(s=je({},s)),o!=null&&!ve(o)&&(o=null);const r=Ll(),i=new WeakSet,l=[];let a=!1;const c=r.app={_uid:Bc++,_component:s,_props:o,_container:null,_context:r,_instance:null,version:Ou,get config(){return r.config},set config(u){},use(u,...f){return i.has(u)||(u&&Z(u.install)?(i.add(u),u.install(c,...f)):Z(u)&&(i.add(u),u(c,...f))),c},mixin(u){return r.mixins.includes(u)||r.mixins.push(u),c},component(u,f){return f?(r.components[u]=f,c):r.components[u]},directive(u,f){return f?(r.directives[u]=f,c):r.directives[u]},mount(u,f,m){if(!a){const v=c._ceVNode||te(s,o);return v.appContext=r,m===!0?m="svg":m===!1&&(m=void 0),e(v,u,m),a=!0,c._container=u,u.__vue_app__=c,Hs(v.component)}},onUnmount(u){l.push(u)},unmount(){a&&(Ct(l,c._instance,16),e(null,c._container),delete c._container.__vue_app__)},provide(u,f){return r.provides[u]=f,c},runWithContext(u){const f=yn;yn=c;try{return u()}finally{yn=f}}};return c}}let yn=null;function fs(e,t){if(We){let n=We.provides;const s=We.parent&&We.parent.provides;s===n&&(n=We.provides=Object.create(s)),n[e]=t}}function It(e,t,n=!1){const s=We||st;if(s||yn){let o=yn?yn._context.provides:s?s.parent==null||s.ce?s.vnode.appContext&&s.vnode.appContext.provides:s.parent.provides:void 0;if(o&&e in o)return o[e];if(arguments.length>1)return n&&Z(t)?t.call(s&&s.proxy):t}}const Cl={},Ol=()=>Object.create(Cl),Pl=e=>Object.getPrototypeOf(e)===Cl;function Yc(e,t,n,s=!1){const o={},r=Ol();e.propsDefaults=Object.create(null),Nl(e,t,o,r);for(const i in e.propsOptions[0])i in o||(o[i]=void 0);n?e.props=s?o:cl(o):e.type.props?e.props=o:e.props=r,e.attrs=r}function zc(e,t,n,s){const{props:o,attrs:r,vnode:{patchFlag:i}}=e,l=ue(o),[a]=e.propsOptions;let c=!1;if((s||i>0)&&!(i&16)){if(i&8){const u=e.vnode.dynamicProps;for(let f=0;f<u.length;f++){let m=u[f];if(Us(e.emitsOptions,m))continue;const v=t[m];if(a)if(fe(r,m))v!==r[m]&&(r[m]=v,c=!0);else{const O=Bt(m);o[O]=br(a,l,O,v,e,!1)}else v!==r[m]&&(r[m]=v,c=!0)}}}else{Nl(e,t,o,r)&&(c=!0);let u;for(const f in l)(!t||!fe(t,f)&&((u=an(f))===f||!fe(t,u)))&&(a?n&&(n[f]!==void 0||n[u]!==void 0)&&(o[f]=br(a,l,f,void 0,e,!0)):delete o[f]);if(r!==l)for(const f in r)(!t||!fe(t,f))&&(delete r[f],c=!0)}c&&kt(e.attrs,"set","")}function Nl(e,t,n,s){const[o,r]=e.propsOptions;let i=!1,l;if(t)for(let a in t){if(wn(a))continue;const c=t[a];let u;o&&fe(o,u=Bt(a))?!r||!r.includes(u)?n[u]=c:(l||(l={}))[u]=c:Us(e.emitsOptions,a)||(!(a in s)||c!==s[a])&&(s[a]=c,i=!0)}if(r){const a=ue(n),c=l||_e;for(let u=0;u<r.length;u++){const f=r[u];n[f]=br(o,a,f,c[f],e,!fe(c,f))}}return i}function br(e,t,n,s,o,r){const i=e[n];if(i!=null){const l=fe(i,"default");if(l&&s===void 0){const a=i.default;if(i.type!==Function&&!i.skipFactory&&Z(a)){const{propsDefaults:c}=o;if(n in c)s=c[n];else{const u=ns(o);s=c[n]=a.call(null,t),u()}}else s=a;o.ce&&o.ce._setProp(n,s)}i[0]&&(r&&!l?s=!1:i[1]&&(s===""||s===an(n))&&(s=!0))}return s}const Xc=new WeakMap;function Al(e,t,n=!1){const s=n?Xc:t.propsCache,o=s.get(e);if(o)return o;const r=e.props,i={},l=[];let a=!1;if(!Z(e)){const u=f=>{a=!0;const[m,v]=Al(f,t,!0);je(i,m),v&&l.push(...v)};!n&&t.mixins.length&&t.mixins.forEach(u),e.extends&&u(e.extends),e.mixins&&e.mixins.forEach(u)}if(!r&&!a)return ve(e)&&s.set(e,pn),pn;if(J(r))for(let u=0;u<r.length;u++){const f=Bt(r[u]);vo(f)&&(i[f]=_e)}else if(r)for(const u in r){const f=Bt(u);if(vo(f)){const m=r[u],v=i[f]=J(m)||Z(m)?{type:m}:je({},m),O=v.type;let C=!1,D=!0;if(J(O))for(let E=0;E<O.length;++E){const F=O[E],b=Z(F)&&F.name;if(b==="Boolean"){C=!0;break}else b==="String"&&(D=!1)}else C=Z(O)&&O.name==="Boolean";v[0]=C,v[1]=D,(C||fe(v,"default"))&&l.push(f)}}const c=[i,l];return ve(e)&&s.set(e,c),c}function vo(e){return e[0]!=="$"&&!wn(e)}const Xr=e=>e[0]==="_"||e==="$stable",qr=e=>J(e)?e.map(yt):[yt(e)],qc=(e,t,n)=>{if(t._n)return t;const s=xe((...o)=>qr(t(...o)),n);return s._c=!1,s},Rl=(e,t,n)=>{const s=e._ctx;for(const o in e){if(Xr(o))continue;const r=e[o];if(Z(r))t[o]=qc(o,r,s);else if(r!=null){const i=qr(r);t[o]=()=>i}}},kl=(e,t)=>{const n=qr(t);e.slots.default=()=>n},wl=(e,t,n)=>{for(const s in t)(n||!Xr(s))&&(e[s]=t[s])},Jc=(e,t,n)=>{const s=e.slots=Ol();if(e.vnode.shapeFlag&32){const o=t._;o?(wl(s,t,n),n&&ji(s,"_",o,!0)):Rl(t,s)}else t&&kl(e,t)},Qc=(e,t,n)=>{const{vnode:s,slots:o}=e;let r=!0,i=_e;if(s.shapeFlag&32){const l=t._;l?n&&l===1?r=!1:wl(o,t,n):(r=!t.$stable,Rl(t,o)),i=t}else t&&(kl(e,t),i={default:1});if(r)for(const l in o)!Xr(l)&&i[l]==null&&delete o[l]},nt=du;function Zc(e){return eu(e)}function eu(e,t){const n=ws();n.__VUE__=!0;const{insert:s,remove:o,patchProp:r,createElement:i,createText:l,createComment:a,setText:c,setElementText:u,parentNode:f,nextSibling:m,setScopeId:v=Tt,insertStaticContent:O}=e,C=(p,g,_,S=null,M=null,k=null,V=void 0,U=null,d=!!g.dynamicChildren)=>{if(p===g)return;p&&!Nn(p,g)&&(S=w(p),Se(p,M,k,!0),p=null),g.patchFlag===-2&&(d=!1,g.dynamicChildren=null);const{type:h,ref:I,shapeFlag:P}=g;switch(h){case ts:D(p,g,_,S);break;case Yt:E(p,g,_,S);break;case ds:p==null&&F(g,_,S,V);break;case Xe:ee(p,g,_,S,M,k,V,U,d);break;default:P&1?R(p,g,_,S,M,k,V,U,d):P&6?me(p,g,_,S,M,k,V,U,d):(P&64||P&128)&&h.process(p,g,_,S,M,k,V,U,d,Y)}I!=null&&M&&Es(I,p&&p.ref,k,g||p,!g)},D=(p,g,_,S)=>{if(p==null)s(g.el=l(g.children),_,S);else{const M=g.el=p.el;g.children!==p.children&&c(M,g.children)}},E=(p,g,_,S)=>{p==null?s(g.el=a(g.children||""),_,S):g.el=p.el},F=(p,g,_,S)=>{[p.el,p.anchor]=O(p.children,g,_,S,p.el,p.anchor)},b=({el:p,anchor:g},_,S)=>{let M;for(;p&&p!==g;)M=m(p),s(p,_,S),p=M;s(g,_,S)},y=({el:p,anchor:g})=>{let _;for(;p&&p!==g;)_=m(p),o(p),p=_;o(g)},R=(p,g,_,S,M,k,V,U,d)=>{g.type==="svg"?V="svg":g.type==="math"&&(V="mathml"),p==null?A(g,_,S,M,k,V,U,d):H(p,g,M,k,V,U,d)},A=(p,g,_,S,M,k,V,U)=>{let d,h;const{props:I,shapeFlag:P,transition:W,dirs:$}=p;if(d=p.el=i(p.type,k,I&&I.is,I),P&8?u(d,p.children):P&16&&B(p.children,d,null,S,M,nr(p,k),V,U),$&&Zt(p,null,S,"created"),x(d,p,p.scopeId,V,S),I){for(const N in I)N!=="value"&&!wn(N)&&r(d,N,null,I[N],k,S);"value"in I&&r(d,"value",null,I.value,k),(h=I.onVnodeBeforeMount)&&vt(h,S,p)}$&&Zt(p,null,S,"beforeMount");const T=tu(M,W);T&&W.beforeEnter(d),s(d,g,_),((h=I&&I.onVnodeMounted)||T||$)&&nt(()=>{h&&vt(h,S,p),T&&W.enter(d),$&&Zt(p,null,S,"mounted")},M)},x=(p,g,_,S,M)=>{if(_&&v(p,_),S)for(let k=0;k<S.length;k++)v(p,S[k]);if(M){let k=M.subTree;if(g===k||Ul(k.type)&&(k.ssContent===g||k.ssFallback===g)){const V=M.vnode;x(p,V,V.scopeId,V.slotScopeIds,M.parent)}}},B=(p,g,_,S,M,k,V,U,d=0)=>{for(let h=d;h<p.length;h++){const I=p[h]=U?Wt(p[h]):yt(p[h]);C(null,I,g,_,S,M,k,V,U)}},H=(p,g,_,S,M,k,V)=>{const U=g.el=p.el;let{patchFlag:d,dynamicChildren:h,dirs:I}=g;d|=p.patchFlag&16;const P=p.props||_e,W=g.props||_e;let $;if(_&&en(_,!1),($=W.onVnodeBeforeUpdate)&&vt($,_,g,p),I&&Zt(g,p,_,"beforeUpdate"),_&&en(_,!0),(P.innerHTML&&W.innerHTML==null||P.textContent&&W.textContent==null)&&u(U,""),h?ae(p.dynamicChildren,h,U,_,S,nr(g,M),k):V||se(p,g,U,null,_,S,nr(g,M),k,!1),d>0){if(d&16)Te(U,P,W,_,M);else if(d&2&&P.class!==W.class&&r(U,"class",null,W.class,M),d&4&&r(U,"style",P.style,W.style,M),d&8){const T=g.dynamicProps;for(let N=0;N<T.length;N++){const z=T[N],re=P[z],ye=W[z];(ye!==re||z==="value")&&r(U,z,re,ye,M,_)}}d&1&&p.children!==g.children&&u(U,g.children)}else!V&&h==null&&Te(U,P,W,_,M);(($=W.onVnodeUpdated)||I)&&nt(()=>{$&&vt($,_,g,p),I&&Zt(g,p,_,"updated")},S)},ae=(p,g,_,S,M,k,V)=>{for(let U=0;U<g.length;U++){const d=p[U],h=g[U],I=d.el&&(d.type===Xe||!Nn(d,h)||d.shapeFlag&198)?f(d.el):_;C(d,h,I,null,S,M,k,V,!0)}},Te=(p,g,_,S,M)=>{if(g!==_){if(g!==_e)for(const k in g)!wn(k)&&!(k in _)&&r(p,k,g[k],null,M,S);for(const k in _){if(wn(k))continue;const V=_[k],U=g[k];V!==U&&k!=="value"&&r(p,k,U,V,M,S)}"value"in _&&r(p,"value",g.value,_.value,M)}},ee=(p,g,_,S,M,k,V,U,d)=>{const h=g.el=p?p.el:l(""),I=g.anchor=p?p.anchor:l("");let{patchFlag:P,dynamicChildren:W,slotScopeIds:$}=g;$&&(U=U?U.concat($):$),p==null?(s(h,_,S),s(I,_,S),B(g.children||[],_,I,M,k,V,U,d)):P>0&&P&64&&W&&p.dynamicChildren?(ae(p.dynamicChildren,W,_,M,k,V,U),(g.key!=null||M&&g===M.subTree)&&Ml(p,g,!0)):se(p,g,_,I,M,k,V,U,d)},me=(p,g,_,S,M,k,V,U,d)=>{g.slotScopeIds=U,p==null?g.shapeFlag&512?M.ctx.activate(g,_,S,V,d):ut(g,_,S,M,k,V,d):ot(p,g,d)},ut=(p,g,_,S,M,k,V)=>{const U=p.component=Eu(p,S,M);if(yl(p)&&(U.ctx.renderer=Y),Tu(U,!1,V),U.asyncDep){if(M&&M.registerDep(U,de,V),!p.el){const d=U.subTree=te(Yt);E(null,d,g,_)}}else de(U,p,g,_,M,k,V)},ot=(p,g,_)=>{const S=g.component=p.component;if(uu(p,g,_))if(S.asyncDep&&!S.asyncResolved){oe(S,g,_);return}else S.next=g,S.update();else g.el=p.el,S.vnode=g},de=(p,g,_,S,M,k,V)=>{const U=()=>{if(p.isMounted){let{next:P,bu:W,u:$,parent:T,vnode:N}=p;{const ke=Fl(p);if(ke){P&&(P.el=N.el,oe(p,P,V)),ke.asyncDep.then(()=>{p.isUnmounted||U()});return}}let z=P,re;en(p,!1),P?(P.el=N.el,oe(p,P,V)):P=N,W&&cs(W),(re=P.props&&P.props.onVnodeBeforeUpdate)&&vt(re,T,P,N),en(p,!0);const ye=yo(p),Be=p.subTree;p.subTree=ye,C(Be,ye,f(Be.el),w(Be),p,M,k),P.el=ye.el,z===null&&fu(p,ye.el),$&&nt($,M),(re=P.props&&P.props.onVnodeUpdated)&&nt(()=>vt(re,T,P,N),M)}else{let P;const{el:W,props:$}=g,{bm:T,m:N,parent:z,root:re,type:ye}=p,Be=Dn(g);en(p,!1),T&&cs(T),!Be&&(P=$&&$.onVnodeBeforeMount)&&vt(P,z,g),en(p,!0);{re.ce&&re.ce._injectChildStyle(ye);const ke=p.subTree=yo(p);C(null,ke,_,S,p,M,k),g.el=ke.el}if(N&&nt(N,M),!Be&&(P=$&&$.onVnodeMounted)){const ke=g;nt(()=>vt(P,z,ke),M)}(g.shapeFlag&256||z&&Dn(z.vnode)&&z.vnode.shapeFlag&256)&&p.a&&nt(p.a,M),p.isMounted=!0,g=_=S=null}};p.scope.on();const d=p.effect=new Xi(U);p.scope.off();const h=p.update=d.run.bind(d),I=p.job=d.runIfDirty.bind(d);I.i=p,I.id=p.uid,d.scheduler=()=>Yr(I),en(p,!0),h()},oe=(p,g,_)=>{g.component=p;const S=p.vnode.props;p.vnode=g,p.next=null,zc(p,g.props,S,_),Qc(p,g.children,_),Ft(),ho(p),Dt()},se=(p,g,_,S,M,k,V,U,d=!1)=>{const h=p&&p.children,I=p?p.shapeFlag:0,P=g.children,{patchFlag:W,shapeFlag:$}=g;if(W>0){if(W&128){qe(h,P,_,S,M,k,V,U,d);return}else if(W&256){Ke(h,P,_,S,M,k,V,U,d);return}}$&8?(I&16&&Ae(h,M,k),P!==h&&u(_,P)):I&16?$&16?qe(h,P,_,S,M,k,V,U,d):Ae(h,M,k,!0):(I&8&&u(_,""),$&16&&B(P,_,S,M,k,V,U,d))},Ke=(p,g,_,S,M,k,V,U,d)=>{p=p||pn,g=g||pn;const h=p.length,I=g.length,P=Math.min(h,I);let W;for(W=0;W<P;W++){const $=g[W]=d?Wt(g[W]):yt(g[W]);C(p[W],$,_,null,M,k,V,U,d)}h>I?Ae(p,M,k,!0,!1,P):B(g,_,S,M,k,V,U,d,P)},qe=(p,g,_,S,M,k,V,U,d)=>{let h=0;const I=g.length;let P=p.length-1,W=I-1;for(;h<=P&&h<=W;){const $=p[h],T=g[h]=d?Wt(g[h]):yt(g[h]);if(Nn($,T))C($,T,_,null,M,k,V,U,d);else break;h++}for(;h<=P&&h<=W;){const $=p[P],T=g[W]=d?Wt(g[W]):yt(g[W]);if(Nn($,T))C($,T,_,null,M,k,V,U,d);else break;P--,W--}if(h>P){if(h<=W){const $=W+1,T=$<I?g[$].el:S;for(;h<=W;)C(null,g[h]=d?Wt(g[h]):yt(g[h]),_,T,M,k,V,U,d),h++}}else if(h>W)for(;h<=P;)Se(p[h],M,k,!0),h++;else{const $=h,T=h,N=new Map;for(h=T;h<=W;h++){const tt=g[h]=d?Wt(g[h]):yt(g[h]);tt.key!=null&&N.set(tt.key,h)}let z,re=0;const ye=W-T+1;let Be=!1,ke=0;const Qt=new Array(ye);for(h=0;h<ye;h++)Qt[h]=0;for(h=$;h<=P;h++){const tt=p[h];if(re>=ye){Se(tt,M,k,!0);continue}let gt;if(tt.key!=null)gt=N.get(tt.key);else for(z=T;z<=W;z++)if(Qt[z-T]===0&&Nn(tt,g[z])){gt=z;break}gt===void 0?Se(tt,M,k,!0):(Qt[gt-T]=h+1,gt>=ke?ke=gt:Be=!0,C(tt,g[gt],_,null,M,k,V,U,d),re++)}const Xs=Be?nu(Qt):pn;for(z=Xs.length-1,h=ye-1;h>=0;h--){const tt=T+h,gt=g[tt],lo=tt+1<I?g[tt+1].el:S;Qt[h]===0?C(null,gt,_,lo,M,k,V,U,d):Be&&(z<0||h!==Xs[z]?Ie(gt,_,lo,2):z--)}}},Ie=(p,g,_,S,M=null)=>{const{el:k,type:V,transition:U,children:d,shapeFlag:h}=p;if(h&6){Ie(p.component.subTree,g,_,S);return}if(h&128){p.suspense.move(g,_,S);return}if(h&64){V.move(p,g,_,Y);return}if(V===Xe){s(k,g,_);for(let P=0;P<d.length;P++)Ie(d[P],g,_,S);s(p.anchor,g,_);return}if(V===ds){b(p,g,_);return}if(S!==2&&h&1&&U)if(S===0)U.beforeEnter(k),s(k,g,_),nt(()=>U.enter(k),M);else{const{leave:P,delayLeave:W,afterLeave:$}=U,T=()=>{p.ctx.isUnmounted?o(k):s(k,g,_)},N=()=>{P(k,()=>{T(),$&&$()})};W?W(k,T,N):N()}else s(k,g,_)},Se=(p,g,_,S=!1,M=!1)=>{const{type:k,props:V,ref:U,children:d,dynamicChildren:h,shapeFlag:I,patchFlag:P,dirs:W,cacheIndex:$}=p;if(P===-2&&(M=!1),U!=null&&(Ft(),Es(U,null,_,p,!0),Dt()),$!=null&&(g.renderCache[$]=void 0),I&256){g.ctx.deactivate(p);return}const T=I&1&&W,N=!Dn(p);let z;if(N&&(z=V&&V.onVnodeBeforeUnmount)&&vt(z,g,p),I&6)pt(p.component,_,S);else{if(I&128){p.suspense.unmount(_,S);return}T&&Zt(p,null,g,"beforeUnmount"),I&64?p.type.remove(p,g,_,Y,S):h&&!h.hasOnce&&(k!==Xe||P>0&&P&64)?Ae(h,g,_,!1,!0):(k===Xe&&P&384||!M&&I&16)&&Ae(d,g,_),S&&it(p)}(N&&(z=V&&V.onVnodeUnmounted)||T)&&nt(()=>{z&&vt(z,g,p),T&&Zt(p,null,g,"unmounted")},_)},it=p=>{const{type:g,el:_,anchor:S,transition:M}=p;if(g===Xe){et(_,S);return}if(g===ds){y(p);return}const k=()=>{o(_),M&&!M.persisted&&M.afterLeave&&M.afterLeave()};if(p.shapeFlag&1&&M&&!M.persisted){const{leave:V,delayLeave:U}=M,d=()=>V(_,k);U?U(p.el,k,d):d()}else k()},et=(p,g)=>{let _;for(;p!==g;)_=m(p),o(p),p=_;o(g)},pt=(p,g,_)=>{const{bum:S,scope:M,job:k,subTree:V,um:U,m:d,a:h,parent:I,slots:{__:P}}=p;bo(d),bo(h),S&&cs(S),I&&J(P)&&P.forEach(W=>{I.renderCache[W]=void 0}),M.stop(),k&&(k.flags|=8,Se(V,p,g,_)),U&&nt(U,g),nt(()=>{p.isUnmounted=!0},g),g&&g.pendingBranch&&!g.isUnmounted&&p.asyncDep&&!p.asyncResolved&&p.suspenseId===g.pendingId&&(g.deps--,g.deps===0&&g.resolve())},Ae=(p,g,_,S=!1,M=!1,k=0)=>{for(let V=k;V<p.length;V++)Se(p[V],g,_,S,M)},w=p=>{if(p.shapeFlag&6)return w(p.component.subTree);if(p.shapeFlag&128)return p.suspense.next();const g=m(p.anchor||p.el),_=g&&g[Cc];return _?m(_):g};let G=!1;const j=(p,g,_)=>{p==null?g._vnode&&Se(g._vnode,null,null,!0):C(g._vnode||null,p,g,null,null,null,_),g._vnode=p,G||(G=!0,ho(),pl(),G=!1)},Y={p:C,um:Se,m:Ie,r:it,mt:ut,mc:B,pc:se,pbc:ae,n:w,o:e};return{render:j,hydrate:void 0,createApp:Gc(j)}}function nr({type:e,props:t},n){return n==="svg"&&e==="foreignObject"||n==="mathml"&&e==="annotation-xml"&&t&&t.encoding&&t.encoding.includes("html")?void 0:n}function en({effect:e,job:t},n){n?(e.flags|=32,t.flags|=4):(e.flags&=-33,t.flags&=-5)}function tu(e,t){return(!e||e&&!e.pendingBranch)&&t&&!t.persisted}function Ml(e,t,n=!1){const s=e.children,o=t.children;if(J(s)&&J(o))for(let r=0;r<s.length;r++){const i=s[r];let l=o[r];l.shapeFlag&1&&!l.dynamicChildren&&((l.patchFlag<=0||l.patchFlag===32)&&(l=o[r]=Wt(o[r]),l.el=i.el),!n&&l.patchFlag!==-2&&Ml(i,l)),l.type===ts&&(l.el=i.el),l.type===Yt&&!l.el&&(l.el=i.el)}}function nu(e){const t=e.slice(),n=[0];let s,o,r,i,l;const a=e.length;for(s=0;s<a;s++){const c=e[s];if(c!==0){if(o=n[n.length-1],e[o]<c){t[s]=o,n.push(s);continue}for(r=0,i=n.length-1;r<i;)l=r+i>>1,e[n[l]]<c?r=l+1:i=l;c<e[n[r]]&&(r>0&&(t[s]=n[r-1]),n[r]=s)}}for(r=n.length,i=n[r-1];r-- >0;)n[r]=i,i=t[i];return n}function Fl(e){const t=e.subTree.component;if(t)return t.asyncDep&&!t.asyncResolved?t:Fl(t)}function bo(e){if(e)for(let t=0;t<e.length;t++)e[t].flags|=8}const su=Symbol.for("v-scx"),ru=()=>It(su);function ln(e,t,n){return Dl(e,t,n)}function Dl(e,t,n=_e){const{immediate:s,deep:o,flush:r,once:i}=n,l=je({},n),a=t&&s||!t&&r!=="post";let c;if(Gn){if(r==="sync"){const v=ru();c=v.__watcherHandles||(v.__watcherHandles=[])}else if(!a){const v=()=>{};return v.stop=Tt,v.resume=Tt,v.pause=Tt,v}}const u=We;l.call=(v,O,C)=>Ct(v,u,O,C);let f=!1;r==="post"?l.scheduler=v=>{nt(v,u&&u.suspense)}:r!=="sync"&&(f=!0,l.scheduler=(v,O)=>{O?v():Yr(v)}),l.augmentJob=v=>{t&&(v.flags|=4),f&&(v.flags|=2,u&&(v.id=u.uid,v.i=u))};const m=Tc(e,t,l);return Gn&&(c?c.push(m):a&&m()),m}function ou(e,t,n){const s=this.proxy,o=Pe(e)?e.includes(".")?xl(s,e):()=>s[e]:e.bind(s,s);let r;Z(t)?r=t:(r=t.handler,n=t);const i=ns(this),l=Dl(o,r.bind(s),n);return i(),l}function xl(e,t){const n=t.split(".");return()=>{let s=e;for(let o=0;o<n.length&&s;o++)s=s[n[o]];return s}}const iu=(e,t)=>t==="modelValue"||t==="model-value"?e.modelModifiers:e[`${t}Modifiers`]||e[`${Bt(t)}Modifiers`]||e[`${an(t)}Modifiers`];function lu(e,t,...n){if(e.isUnmounted)return;const s=e.vnode.props||_e;let o=n;const r=t.startsWith("update:"),i=r&&iu(s,t.slice(7));i&&(i.trim&&(o=n.map(u=>Pe(u)?u.trim():u)),i.number&&(o=n.map(ps)));let l,a=s[l=qs(t)]||s[l=qs(Bt(t))];!a&&r&&(a=s[l=qs(an(t))]),a&&Ct(a,e,6,o);const c=s[l+"Once"];if(c){if(!e.emitted)e.emitted={};else if(e.emitted[l])return;e.emitted[l]=!0,Ct(c,e,6,o)}}function $l(e,t,n=!1){const s=t.emitsCache,o=s.get(e);if(o!==void 0)return o;const r=e.emits;let i={},l=!1;if(!Z(e)){const a=c=>{const u=$l(c,t,!0);u&&(l=!0,je(i,u))};!n&&t.mixins.length&&t.mixins.forEach(a),e.extends&&a(e.extends),e.mixins&&e.mixins.forEach(a)}return!r&&!l?(ve(e)&&s.set(e,null),null):(J(r)?r.forEach(a=>i[a]=null):je(i,r),ve(e)&&s.set(e,i),i)}function Us(e,t){return!e||!As(t)?!1:(t=t.slice(2).replace(/Once$/,""),fe(e,t[0].toLowerCase()+t.slice(1))||fe(e,an(t))||fe(e,t))}function yo(e){const{type:t,vnode:n,proxy:s,withProxy:o,propsOptions:[r],slots:i,attrs:l,emit:a,render:c,renderCache:u,props:f,data:m,setupState:v,ctx:O,inheritAttrs:C}=e,D=ys(e);let E,F;try{if(n.shapeFlag&4){const y=o||s,R=y;E=yt(c.call(R,y,u,f,v,m,O)),F=l}else{const y=t;E=yt(y.length>1?y(f,{attrs:l,slots:i,emit:a}):y(f,null)),F=t.props?l:au(l)}}catch(y){$n.length=0,xs(y,e,1),E=te(Yt)}let b=E;if(F&&C!==!1){const y=Object.keys(F),{shapeFlag:R}=b;y.length&&R&7&&(r&&y.some(Fr)&&(F=cu(F,r)),b=Tn(b,F,!1,!0))}return n.dirs&&(b=Tn(b,null,!1,!0),b.dirs=b.dirs?b.dirs.concat(n.dirs):n.dirs),n.transition&&zr(b,n.transition),E=b,ys(D),E}const au=e=>{let t;for(const n in e)(n==="class"||n==="style"||As(n))&&((t||(t={}))[n]=e[n]);return t},cu=(e,t)=>{const n={};for(const s in e)(!Fr(s)||!(s.slice(9)in t))&&(n[s]=e[s]);return n};function uu(e,t,n){const{props:s,children:o,component:r}=e,{props:i,children:l,patchFlag:a}=t,c=r.emitsOptions;if(t.dirs||t.transition)return!0;if(n&&a>=0){if(a&1024)return!0;if(a&16)return s?Eo(s,i,c):!!i;if(a&8){const u=t.dynamicProps;for(let f=0;f<u.length;f++){const m=u[f];if(i[m]!==s[m]&&!Us(c,m))return!0}}}else return(o||l)&&(!l||!l.$stable)?!0:s===i?!1:s?i?Eo(s,i,c):!0:!!i;return!1}function Eo(e,t,n){const s=Object.keys(t);if(s.length!==Object.keys(e).length)return!0;for(let o=0;o<s.length;o++){const r=s[o];if(t[r]!==e[r]&&!Us(n,r))return!0}return!1}function fu({vnode:e,parent:t},n){for(;t;){const s=t.subTree;if(s.suspense&&s.suspense.activeBranch===e&&(s.el=e.el),s===e)(e=t.vnode).el=n,t=t.parent;else break}}const Ul=e=>e.__isSuspense;function du(e,t){t&&t.pendingBranch?J(e)?t.effects.push(...e):t.effects.push(e):Lc(e)}const Xe=Symbol.for("v-fgt"),ts=Symbol.for("v-txt"),Yt=Symbol.for("v-cmt"),ds=Symbol.for("v-stc"),$n=[];let rt=null;function we(e=!1){$n.push(rt=e?null:[])}function hu(){$n.pop(),rt=$n[$n.length-1]||null}let Kn=1;function To(e,t=!1){Kn+=e,e<0&&rt&&t&&(rt.hasOnce=!0)}function Hl(e){return e.dynamicChildren=Kn>0?rt||pn:null,hu(),Kn>0&&rt&&rt.push(e),e}function $e(e,t,n,s,o,r){return Hl(L(e,t,n,s,o,r,!0))}function mu(e,t,n,s,o){return Hl(te(e,t,n,s,o,!0))}function Is(e){return e?e.__v_isVNode===!0:!1}function Nn(e,t){return e.type===t.type&&e.key===t.key}const Vl=({key:e})=>e??null,hs=({ref:e,ref_key:t,ref_for:n})=>(typeof e=="number"&&(e=""+e),e!=null?Pe(e)||Fe(e)||Z(e)?{i:st,r:e,k:t,f:!!n}:e:null);function L(e,t=null,n=null,s=0,o=null,r=e===Xe?0:1,i=!1,l=!1){const a={__v_isVNode:!0,__v_skip:!0,type:e,props:t,key:t&&Vl(t),ref:t&&hs(t),scopeId:vl,slotScopeIds:null,children:n,component:null,suspense:null,ssContent:null,ssFallback:null,dirs:null,transition:null,el:null,anchor:null,target:null,targetStart:null,targetAnchor:null,staticCount:0,shapeFlag:r,patchFlag:s,dynamicProps:o,dynamicChildren:null,appContext:null,ctx:st};return l?(Jr(a,n),r&128&&e.normalize(a)):n&&(a.shapeFlag|=Pe(n)?8:16),Kn>0&&!i&&rt&&(a.patchFlag>0||r&6)&&a.patchFlag!==32&&rt.push(a),a}const te=_u;function _u(e,t=null,n=null,s=0,o=null,r=!1){if((!e||e===$c)&&(e=Yt),Is(e)){const l=Tn(e,t,!0);return n&&Jr(l,n),Kn>0&&!r&&rt&&(l.shapeFlag&6?rt[rt.indexOf(e)]=l:rt.push(l)),l.patchFlag=-2,l}if(Cu(e)&&(e=e.__vccOpts),t){t=pu(t);let{class:l,style:a}=t;l&&!Pe(l)&&(t.class=Oe(l)),ve(a)&&(Kr(a)&&!J(a)&&(a=je({},a)),t.style=Zn(a))}const i=Pe(e)?1:Ul(e)?128:Oc(e)?64:ve(e)?4:Z(e)?2:0;return L(e,t,n,s,o,i,r,!0)}function pu(e){return e?Kr(e)||Pl(e)?je({},e):e:null}function Tn(e,t,n=!1,s=!1){const{props:o,ref:r,patchFlag:i,children:l,transition:a}=e,c=t?vu(o||{},t):o,u={__v_isVNode:!0,__v_skip:!0,type:e.type,props:c,key:c&&Vl(c),ref:t&&t.ref?n&&r?J(r)?r.concat(hs(t)):[r,hs(t)]:hs(t):r,scopeId:e.scopeId,slotScopeIds:e.slotScopeIds,children:l,target:e.target,targetStart:e.targetStart,targetAnchor:e.targetAnchor,staticCount:e.staticCount,shapeFlag:e.shapeFlag,patchFlag:t&&e.type!==Xe?i===-1?16:i|16:i,dynamicProps:e.dynamicProps,dynamicChildren:e.dynamicChildren,appContext:e.appContext,dirs:e.dirs,transition:a,component:e.component,suspense:e.suspense,ssContent:e.ssContent&&Tn(e.ssContent),ssFallback:e.ssFallback&&Tn(e.ssFallback),el:e.el,anchor:e.anchor,ctx:e.ctx,ce:e.ce};return a&&s&&zr(u,a.clone(u)),u}function lt(e=" ",t=0){return te(ts,null,e,t)}function Ss(e,t){const n=te(ds,null,e);return n.staticCount=t,n}function gu(e="",t=!1){return t?(we(),mu(Yt,null,e)):te(Yt,null,e)}function yt(e){return e==null||typeof e=="boolean"?te(Yt):J(e)?te(Xe,null,e.slice()):Is(e)?Wt(e):te(ts,null,String(e))}function Wt(e){return e.el===null&&e.patchFlag!==-1||e.memo?e:Tn(e)}function Jr(e,t){let n=0;const{shapeFlag:s}=e;if(t==null)t=null;else if(J(t))n=16;else if(typeof t=="object")if(s&65){const o=t.default;o&&(o._c&&(o._d=!1),Jr(e,o()),o._c&&(o._d=!0));return}else{n=32;const o=t._;!o&&!Pl(t)?t._ctx=st:o===3&&st&&(st.slots._===1?t._=1:(t._=2,e.patchFlag|=1024))}else Z(t)?(t={default:t,_ctx:st},n=32):(t=String(t),s&64?(n=16,t=[lt(t)]):n=8);e.children=t,e.shapeFlag|=n}function vu(...e){const t={};for(let n=0;n<e.length;n++){const s=e[n];for(const o in s)if(o==="class")t.class!==s.class&&(t.class=Oe([t.class,s.class]));else if(o==="style")t.style=Zn([t.style,s.style]);else if(As(o)){const r=t[o],i=s[o];i&&r!==i&&!(J(r)&&r.includes(i))&&(t[o]=r?[].concat(r,i):i)}else o!==""&&(t[o]=s[o])}return t}function vt(e,t,n,s=null){Ct(e,t,7,[n,s])}const bu=Ll();let yu=0;function Eu(e,t,n){const s=e.type,o=(t?t.appContext:e.appContext)||bu,r={uid:yu++,vnode:e,type:s,parent:t,appContext:o,root:null,next:null,subTree:null,effect:null,update:null,job:null,scope:new Yi(!0),render:null,proxy:null,exposed:null,exposeProxy:null,withProxy:null,provides:t?t.provides:Object.create(o.provides),ids:t?t.ids:["",0,0],accessCache:null,renderCache:[],components:null,directives:null,propsOptions:Al(s,o),emitsOptions:$l(s,o),emit:null,emitted:null,propsDefaults:_e,inheritAttrs:s.inheritAttrs,ctx:_e,data:_e,props:_e,attrs:_e,slots:_e,refs:_e,setupState:_e,setupContext:null,suspense:n,suspenseId:n?n.pendingId:0,asyncDep:null,asyncResolved:!1,isMounted:!1,isUnmounted:!1,isDeactivated:!1,bc:null,c:null,bm:null,m:null,bu:null,u:null,um:null,bum:null,da:null,a:null,rtg:null,rtc:null,ec:null,sp:null};return r.ctx={_:r},r.root=t?t.root:r,r.emit=lu.bind(null,r),e.ce&&e.ce(r),r}let We=null;const Bn=()=>We||st;let Ls,yr;{const e=ws(),t=(n,s)=>{let o;return(o=e[n])||(o=e[n]=[]),o.push(s),r=>{o.length>1?o.forEach(i=>i(r)):o[0](r)}};Ls=t("__VUE_INSTANCE_SETTERS__",n=>We=n),yr=t("__VUE_SSR_SETTERS__",n=>Gn=n)}const ns=e=>{const t=We;return Ls(e),e.scope.on(),()=>{e.scope.off(),Ls(t)}},Io=()=>{We&&We.scope.off(),Ls(null)};function Wl(e){return e.vnode.shapeFlag&4}let Gn=!1;function Tu(e,t=!1,n=!1){t&&yr(t);const{props:s,children:o}=e.vnode,r=Wl(e);Yc(e,s,r,t),Jc(e,o,n||t);const i=r?Iu(e,t):void 0;return t&&yr(!1),i}function Iu(e,t){const n=e.type;e.accessCache=Object.create(null),e.proxy=new Proxy(e.ctx,Uc);const{setup:s}=n;if(s){Ft();const o=e.setupContext=s.length>1?Lu(e):null,r=ns(e),i=es(s,e,0,[e.props,o]),l=Ui(i);if(Dt(),r(),(l||e.sp)&&!Dn(e)&&bl(e),l){if(i.then(Io,Io),t)return i.then(a=>{So(e,a)}).catch(a=>{xs(a,e,0)});e.asyncDep=i}else So(e,i)}else jl(e)}function So(e,t,n){Z(t)?e.type.__ssrInlineRender?e.ssrRender=t:e.render=t:ve(t)&&(e.setupState=hl(t)),jl(e)}function jl(e,t,n){const s=e.type;e.render||(e.render=s.render||Tt);{const o=ns(e);Ft();try{Hc(e)}finally{Dt(),o()}}}const Su={get(e,t){return He(e,"get",""),e[t]}};function Lu(e){const t=n=>{e.exposed=n||{}};return{attrs:new Proxy(e.attrs,Su),slots:e.slots,emit:e.emit,expose:t}}function Hs(e){return e.exposed?e.exposeProxy||(e.exposeProxy=new Proxy(hl(fl(e.exposed)),{get(t,n){if(n in t)return t[n];if(n in xn)return xn[n](e)},has(t,n){return n in t||n in xn}})):e.proxy}function Cu(e){return Z(e)&&"__vccOpts"in e}const Ee=(e,t)=>yc(e,t,Gn);function Vs(e,t,n){const s=arguments.length;return s===2?ve(t)&&!J(t)?Is(t)?te(e,null,[t]):te(e,t):te(e,null,t):(s>3?n=Array.prototype.slice.call(arguments,2):s===3&&Is(n)&&(n=[n]),te(e,t,n))}const Ou="3.5.16";/**
* @vue/runtime-dom v3.5.16
* (c) 2018-present Yuxi (Evan) You and Vue contributors
* @license MIT
**/let Er;const Lo=typeof window<"u"&&window.trustedTypes;if(Lo)try{Er=Lo.createPolicy("vue",{createHTML:e=>e})}catch{}const Kl=Er?e=>Er.createHTML(e):e=>e,Pu="http://www.w3.org/2000/svg",Nu="http://www.w3.org/1998/Math/MathML",Rt=typeof document<"u"?document:null,Co=Rt&&Rt.createElement("template"),Au={insert:(e,t,n)=>{t.insertBefore(e,n||null)},remove:e=>{const t=e.parentNode;t&&t.removeChild(e)},createElement:(e,t,n,s)=>{const o=t==="svg"?Rt.createElementNS(Pu,e):t==="mathml"?Rt.createElementNS(Nu,e):n?Rt.createElement(e,{is:n}):Rt.createElement(e);return e==="select"&&s&&s.multiple!=null&&o.setAttribute("multiple",s.multiple),o},createText:e=>Rt.createTextNode(e),createComment:e=>Rt.createComment(e),setText:(e,t)=>{e.nodeValue=t},setElementText:(e,t)=>{e.textContent=t},parentNode:e=>e.parentNode,nextSibling:e=>e.nextSibling,querySelector:e=>Rt.querySelector(e),setScopeId(e,t){e.setAttribute(t,"")},insertStaticContent(e,t,n,s,o,r){const i=n?n.previousSibling:t.lastChild;if(o&&(o===r||o.nextSibling))for(;t.insertBefore(o.cloneNode(!0),n),!(o===r||!(o=o.nextSibling)););else{Co.innerHTML=Kl(s==="svg"?`<svg>${e}</svg>`:s==="mathml"?`<math>${e}</math>`:e);const l=Co.content;if(s==="svg"||s==="mathml"){const a=l.firstChild;for(;a.firstChild;)l.appendChild(a.firstChild);l.removeChild(a)}t.insertBefore(l,n)}return[i?i.nextSibling:t.firstChild,n?n.previousSibling:t.lastChild]}},Ru=Symbol("_vtc");function ku(e,t,n){const s=e[Ru];s&&(t=(t?[t,...s]:[...s]).join(" ")),t==null?e.removeAttribute("class"):n?e.setAttribute("class",t):e.className=t}const Oo=Symbol("_vod"),wu=Symbol("_vsh"),Mu=Symbol(""),Fu=/(^|;)\s*display\s*:/;function Du(e,t,n){const s=e.style,o=Pe(n);let r=!1;if(n&&!o){if(t)if(Pe(t))for(const i of t.split(";")){const l=i.slice(0,i.indexOf(":")).trim();n[l]==null&&ms(s,l,"")}else for(const i in t)n[i]==null&&ms(s,i,"");for(const i in n)i==="display"&&(r=!0),ms(s,i,n[i])}else if(o){if(t!==n){const i=s[Mu];i&&(n+=";"+i),s.cssText=n,r=Fu.test(n)}}else t&&e.removeAttribute("style");Oo in e&&(e[Oo]=r?s.display:"",e[wu]&&(s.display="none"))}const Po=/\s*!important$/;function ms(e,t,n){if(J(n))n.forEach(s=>ms(e,t,s));else if(n==null&&(n=""),t.startsWith("--"))e.setProperty(t,n);else{const s=xu(e,t);Po.test(n)?e.setProperty(an(s),n.replace(Po,""),"important"):e[s]=n}}const No=["Webkit","Moz","ms"],sr={};function xu(e,t){const n=sr[t];if(n)return n;let s=Bt(t);if(s!=="filter"&&s in e)return sr[t]=s;s=Wi(s);for(let o=0;o<No.length;o++){const r=No[o]+s;if(r in e)return sr[t]=r}return t}const Ao="http://www.w3.org/1999/xlink";function Ro(e,t,n,s,o,r=Xa(t)){s&&t.startsWith("xlink:")?n==null?e.removeAttributeNS(Ao,t.slice(6,t.length)):e.setAttributeNS(Ao,t,n):n==null||r&&!Ki(n)?e.removeAttribute(t):e.setAttribute(t,r?"":Lt(n)?String(n):n)}function ko(e,t,n,s,o){if(t==="innerHTML"||t==="textContent"){n!=null&&(e[t]=t==="innerHTML"?Kl(n):n);return}const r=e.tagName;if(t==="value"&&r!=="PROGRESS"&&!r.includes("-")){const l=r==="OPTION"?e.getAttribute("value")||"":e.value,a=n==null?e.type==="checkbox"?"on":"":String(n);(l!==a||!("_value"in e))&&(e.value=a),n==null&&e.removeAttribute(t),e._value=n;return}let i=!1;if(n===""||n==null){const l=typeof e[t];l==="boolean"?n=Ki(n):n==null&&l==="string"?(n="",i=!0):l==="number"&&(n=0,i=!0)}try{e[t]=n}catch{}i&&e.removeAttribute(o||t)}function rn(e,t,n,s){e.addEventListener(t,n,s)}function $u(e,t,n,s){e.removeEventListener(t,n,s)}const wo=Symbol("_vei");function Uu(e,t,n,s,o=null){const r=e[wo]||(e[wo]={}),i=r[t];if(s&&i)i.value=s;else{const[l,a]=Hu(t);if(s){const c=r[t]=ju(s,o);rn(e,l,c,a)}else i&&($u(e,l,i,a),r[t]=void 0)}}const Mo=/(?:Once|Passive|Capture)$/;function Hu(e){let t;if(Mo.test(e)){t={};let s;for(;s=e.match(Mo);)e=e.slice(0,e.length-s[0].length),t[s[0].toLowerCase()]=!0}return[e[2]===":"?e.slice(3):an(e.slice(2)),t]}let rr=0;const Vu=Promise.resolve(),Wu=()=>rr||(Vu.then(()=>rr=0),rr=Date.now());function ju(e,t){const n=s=>{if(!s._vts)s._vts=Date.now();else if(s._vts<=n.attached)return;Ct(Ku(s,n.value),t,5,[s])};return n.value=e,n.attached=Wu(),n}function Ku(e,t){if(J(t)){const n=e.stopImmediatePropagation;return e.stopImmediatePropagation=()=>{n.call(e),e._stopped=!0},t.map(s=>o=>!o._stopped&&s&&s(o))}else return t}const Fo=e=>e.charCodeAt(0)===111&&e.charCodeAt(1)===110&&e.charCodeAt(2)>96&&e.charCodeAt(2)<123,Bu=(e,t,n,s,o,r)=>{const i=o==="svg";t==="class"?ku(e,s,i):t==="style"?Du(e,n,s):As(t)?Fr(t)||Uu(e,t,n,s,r):(t[0]==="."?(t=t.slice(1),!0):t[0]==="^"?(t=t.slice(1),!1):Gu(e,t,s,i))?(ko(e,t,s),!e.tagName.includes("-")&&(t==="value"||t==="checked"||t==="selected")&&Ro(e,t,s,i,r,t!=="value")):e._isVueCE&&(/[A-Z]/.test(t)||!Pe(s))?ko(e,Bt(t),s,r,t):(t==="true-value"?e._trueValue=s:t==="false-value"&&(e._falseValue=s),Ro(e,t,s,i))};function Gu(e,t,n,s){if(s)return!!(t==="innerHTML"||t==="textContent"||t in e&&Fo(t)&&Z(n));if(t==="spellcheck"||t==="draggable"||t==="translate"||t==="autocorrect"||t==="form"||t==="list"&&e.tagName==="INPUT"||t==="type"&&e.tagName==="TEXTAREA")return!1;if(t==="width"||t==="height"){const o=e.tagName;if(o==="IMG"||o==="VIDEO"||o==="CANVAS"||o==="SOURCE")return!1}return Fo(t)&&Pe(n)?!1:t in e}const Cs=e=>{const t=e.props["onUpdate:modelValue"]||!1;return J(t)?n=>cs(t,n):t};function Yu(e){e.target.composing=!0}function Do(e){const t=e.target;t.composing&&(t.composing=!1,t.dispatchEvent(new Event("input")))}const En=Symbol("_assign"),Kp={created(e,{modifiers:{lazy:t,trim:n,number:s}},o){e[En]=Cs(o);const r=s||o.props&&o.props.type==="number";rn(e,t?"change":"input",i=>{if(i.target.composing)return;let l=e.value;n&&(l=l.trim()),r&&(l=ps(l)),e[En](l)}),n&&rn(e,"change",()=>{e.value=e.value.trim()}),t||(rn(e,"compositionstart",Yu),rn(e,"compositionend",Do),rn(e,"change",Do))},mounted(e,{value:t}){e.value=t??""},beforeUpdate(e,{value:t,oldValue:n,modifiers:{lazy:s,trim:o,number:r}},i){if(e[En]=Cs(i),e.composing)return;const l=(r||e.type==="number")&&!/^0\d/.test(e.value)?ps(e.value):e.value,a=t??"";l!==a&&(document.activeElement===e&&e.type!=="range"&&(s&&t===n||o&&e.value.trim()===a)||(e.value=a))}},Bp={deep:!0,created(e,{value:t,modifiers:{number:n}},s){const o=Rs(t);rn(e,"change",()=>{const r=Array.prototype.filter.call(e.options,i=>i.selected).map(i=>n?ps(Os(i)):Os(i));e[En](e.multiple?o?new Set(r):r:r[0]),e._assigning=!0,Gr(()=>{e._assigning=!1})}),e[En]=Cs(s)},mounted(e,{value:t}){xo(e,t)},beforeUpdate(e,t,n){e[En]=Cs(n)},updated(e,{value:t}){e._assigning||xo(e,t)}};function xo(e,t){const n=e.multiple,s=J(t);if(!(n&&!s&&!Rs(t))){for(let o=0,r=e.options.length;o<r;o++){const i=e.options[o],l=Os(i);if(n)if(s){const a=typeof l;a==="string"||a==="number"?i.selected=t.some(c=>String(c)===String(l)):i.selected=Ja(t,l)>-1}else i.selected=t.has(l);else if(Ms(Os(i),t)){e.selectedIndex!==o&&(e.selectedIndex=o);return}}!n&&e.selectedIndex!==-1&&(e.selectedIndex=-1)}}function Os(e){return"_value"in e?e._value:e.value}const zu=["ctrl","shift","alt","meta"],Xu={stop:e=>e.stopPropagation(),prevent:e=>e.preventDefault(),self:e=>e.target!==e.currentTarget,ctrl:e=>!e.ctrlKey,shift:e=>!e.shiftKey,alt:e=>!e.altKey,meta:e=>!e.metaKey,left:e=>"button"in e&&e.button!==0,middle:e=>"button"in e&&e.button!==1,right:e=>"button"in e&&e.button!==2,exact:(e,t)=>zu.some(n=>e[`${n}Key`]&&!t.includes(n))},Gp=(e,t)=>{const n=e._withMods||(e._withMods={}),s=t.join(".");return n[s]||(n[s]=(o,...r)=>{for(let i=0;i<t.length;i++){const l=Xu[t[i]];if(l&&l(o,t))return}return e(o,...r)})},qu=je({patchProp:Bu},Au);let $o;function Ju(){return $o||($o=Zc(qu))}const Qu=(...e)=>{const t=Ju().createApp(...e),{mount:n}=t;return t.mount=s=>{const o=ef(s);if(!o)return;const r=t._component;!Z(r)&&!r.render&&!r.template&&(r.template=o.innerHTML),o.nodeType===1&&(o.textContent="");const i=n(o,!1,Zu(o));return o instanceof Element&&(o.removeAttribute("v-cloak"),o.setAttribute("data-v-app","")),i},t};function Zu(e){if(e instanceof SVGElement)return"svg";if(typeof MathMLElement=="function"&&e instanceof MathMLElement)return"mathml"}function ef(e){return Pe(e)?document.querySelector(e):e}/*!
 * pinia v3.0.2
 * (c) 2025 Eduardo San Martin Morote
 * @license MIT
 */const tf=Symbol();var Uo;(function(e){e.direct="direct",e.patchObject="patch object",e.patchFunction="patch function"})(Uo||(Uo={}));function nf(){const e=zi(!0),t=e.run(()=>Ve({}));let n=[],s=[];const o=fl({install(r){o._a=r,r.provide(tf,o),r.config.globalProperties.$pinia=o,s.forEach(i=>n.push(i)),s=[]},use(r){return this._a?n.push(r):s.push(r),this},_p:n,_a:null,_e:e,_s:new Map,state:t});return o}/*!
  * vue-router v4.5.1
  * (c) 2025 Eduardo San Martin Morote
  * @license MIT
  */const hn=typeof document<"u";function Bl(e){return typeof e=="object"||"displayName"in e||"props"in e||"__vccOpts"in e}function sf(e){return e.__esModule||e[Symbol.toStringTag]==="Module"||e.default&&Bl(e.default)}const ce=Object.assign;function or(e,t){const n={};for(const s in t){const o=t[s];n[s]=_t(o)?o.map(e):e(o)}return n}const Un=()=>{},_t=Array.isArray,Gl=/#/g,rf=/&/g,of=/\//g,lf=/=/g,af=/\?/g,Yl=/\+/g,cf=/%5B/g,uf=/%5D/g,zl=/%5E/g,ff=/%60/g,Xl=/%7B/g,df=/%7C/g,ql=/%7D/g,hf=/%20/g;function Qr(e){return encodeURI(""+e).replace(df,"|").replace(cf,"[").replace(uf,"]")}function mf(e){return Qr(e).replace(Xl,"{").replace(ql,"}").replace(zl,"^")}function Tr(e){return Qr(e).replace(Yl,"%2B").replace(hf,"+").replace(Gl,"%23").replace(rf,"%26").replace(ff,"`").replace(Xl,"{").replace(ql,"}").replace(zl,"^")}function _f(e){return Tr(e).replace(lf,"%3D")}function pf(e){return Qr(e).replace(Gl,"%23").replace(af,"%3F")}function gf(e){return e==null?"":pf(e).replace(of,"%2F")}function Yn(e){try{return decodeURIComponent(""+e)}catch{}return""+e}const vf=/\/$/,bf=e=>e.replace(vf,"");function ir(e,t,n="/"){let s,o={},r="",i="";const l=t.indexOf("#");let a=t.indexOf("?");return l<a&&l>=0&&(a=-1),a>-1&&(s=t.slice(0,a),r=t.slice(a+1,l>-1?l:t.length),o=e(r)),l>-1&&(s=s||t.slice(0,l),i=t.slice(l,t.length)),s=If(s??t,n),{fullPath:s+(r&&"?")+r+i,path:s,query:o,hash:Yn(i)}}function yf(e,t){const n=t.query?e(t.query):"";return t.path+(n&&"?")+n+(t.hash||"")}function Ho(e,t){return!t||!e.toLowerCase().startsWith(t.toLowerCase())?e:e.slice(t.length)||"/"}function Ef(e,t,n){const s=t.matched.length-1,o=n.matched.length-1;return s>-1&&s===o&&In(t.matched[s],n.matched[o])&&Jl(t.params,n.params)&&e(t.query)===e(n.query)&&t.hash===n.hash}function In(e,t){return(e.aliasOf||e)===(t.aliasOf||t)}function Jl(e,t){if(Object.keys(e).length!==Object.keys(t).length)return!1;for(const n in e)if(!Tf(e[n],t[n]))return!1;return!0}function Tf(e,t){return _t(e)?Vo(e,t):_t(t)?Vo(t,e):e===t}function Vo(e,t){return _t(t)?e.length===t.length&&e.every((n,s)=>n===t[s]):e.length===1&&e[0]===t}function If(e,t){if(e.startsWith("/"))return e;if(!e)return t;const n=t.split("/"),s=e.split("/"),o=s[s.length-1];(o===".."||o===".")&&s.push("");let r=n.length-1,i,l;for(i=0;i<s.length;i++)if(l=s[i],l!==".")if(l==="..")r>1&&r--;else break;return n.slice(0,r).join("/")+"/"+s.slice(i).join("/")}const Ut={path:"/",name:void 0,params:{},query:{},hash:"",fullPath:"/",matched:[],meta:{},redirectedFrom:void 0};var zn;(function(e){e.pop="pop",e.push="push"})(zn||(zn={}));var Hn;(function(e){e.back="back",e.forward="forward",e.unknown=""})(Hn||(Hn={}));function Sf(e){if(!e)if(hn){const t=document.querySelector("base");e=t&&t.getAttribute("href")||"/",e=e.replace(/^\w+:\/\/[^\/]+/,"")}else e="/";return e[0]!=="/"&&e[0]!=="#"&&(e="/"+e),bf(e)}const Lf=/^[^#]+#/;function Cf(e,t){return e.replace(Lf,"#")+t}function Of(e,t){const n=document.documentElement.getBoundingClientRect(),s=e.getBoundingClientRect();return{behavior:t.behavior,left:s.left-n.left-(t.left||0),top:s.top-n.top-(t.top||0)}}const Ws=()=>({left:window.scrollX,top:window.scrollY});function Pf(e){let t;if("el"in e){const n=e.el,s=typeof n=="string"&&n.startsWith("#"),o=typeof n=="string"?s?document.getElementById(n.slice(1)):document.querySelector(n):n;if(!o)return;t=Of(o,e)}else t=e;"scrollBehavior"in document.documentElement.style?window.scrollTo(t):window.scrollTo(t.left!=null?t.left:window.scrollX,t.top!=null?t.top:window.scrollY)}function Wo(e,t){return(history.state?history.state.position-t:-1)+e}const Ir=new Map;function Nf(e,t){Ir.set(e,t)}function Af(e){const t=Ir.get(e);return Ir.delete(e),t}let Rf=()=>location.protocol+"//"+location.host;function Ql(e,t){const{pathname:n,search:s,hash:o}=t,r=e.indexOf("#");if(r>-1){let l=o.includes(e.slice(r))?e.slice(r).length:1,a=o.slice(l);return a[0]!=="/"&&(a="/"+a),Ho(a,"")}return Ho(n,e)+s+o}function kf(e,t,n,s){let o=[],r=[],i=null;const l=({state:m})=>{const v=Ql(e,location),O=n.value,C=t.value;let D=0;if(m){if(n.value=v,t.value=m,i&&i===O){i=null;return}D=C?m.position-C.position:0}else s(v);o.forEach(E=>{E(n.value,O,{delta:D,type:zn.pop,direction:D?D>0?Hn.forward:Hn.back:Hn.unknown})})};function a(){i=n.value}function c(m){o.push(m);const v=()=>{const O=o.indexOf(m);O>-1&&o.splice(O,1)};return r.push(v),v}function u(){const{history:m}=window;m.state&&m.replaceState(ce({},m.state,{scroll:Ws()}),"")}function f(){for(const m of r)m();r=[],window.removeEventListener("popstate",l),window.removeEventListener("beforeunload",u)}return window.addEventListener("popstate",l),window.addEventListener("beforeunload",u,{passive:!0}),{pauseListeners:a,listen:c,destroy:f}}function jo(e,t,n,s=!1,o=!1){return{back:e,current:t,forward:n,replaced:s,position:window.history.length,scroll:o?Ws():null}}function wf(e){const{history:t,location:n}=window,s={value:Ql(e,n)},o={value:t.state};o.value||r(s.value,{back:null,current:s.value,forward:null,position:t.length-1,replaced:!0,scroll:null},!0);function r(a,c,u){const f=e.indexOf("#"),m=f>-1?(n.host&&document.querySelector("base")?e:e.slice(f))+a:Rf()+e+a;try{t[u?"replaceState":"pushState"](c,"",m),o.value=c}catch(v){console.error(v),n[u?"replace":"assign"](m)}}function i(a,c){const u=ce({},t.state,jo(o.value.back,a,o.value.forward,!0),c,{position:o.value.position});r(a,u,!0),s.value=a}function l(a,c){const u=ce({},o.value,t.state,{forward:a,scroll:Ws()});r(u.current,u,!0);const f=ce({},jo(s.value,a,null),{position:u.position+1},c);r(a,f,!1),s.value=a}return{location:s,state:o,push:l,replace:i}}function Mf(e){e=Sf(e);const t=wf(e),n=kf(e,t.state,t.location,t.replace);function s(r,i=!0){i||n.pauseListeners(),history.go(r)}const o=ce({location:"",base:e,go:s,createHref:Cf.bind(null,e)},t,n);return Object.defineProperty(o,"location",{enumerable:!0,get:()=>t.location.value}),Object.defineProperty(o,"state",{enumerable:!0,get:()=>t.state.value}),o}function Ff(e){return typeof e=="string"||e&&typeof e=="object"}function Zl(e){return typeof e=="string"||typeof e=="symbol"}const ea=Symbol("");var Ko;(function(e){e[e.aborted=4]="aborted",e[e.cancelled=8]="cancelled",e[e.duplicated=16]="duplicated"})(Ko||(Ko={}));function Sn(e,t){return ce(new Error,{type:e,[ea]:!0},t)}function Pt(e,t){return e instanceof Error&&ea in e&&(t==null||!!(e.type&t))}const Bo="[^/]+?",Df={sensitive:!1,strict:!1,start:!0,end:!0},xf=/[.+*?^${}()[\]/\\]/g;function $f(e,t){const n=ce({},Df,t),s=[];let o=n.start?"^":"";const r=[];for(const c of e){const u=c.length?[]:[90];n.strict&&!c.length&&(o+="/");for(let f=0;f<c.length;f++){const m=c[f];let v=40+(n.sensitive?.25:0);if(m.type===0)f||(o+="/"),o+=m.value.replace(xf,"\\$&"),v+=40;else if(m.type===1){const{value:O,repeatable:C,optional:D,regexp:E}=m;r.push({name:O,repeatable:C,optional:D});const F=E||Bo;if(F!==Bo){v+=10;try{new RegExp(`(${F})`)}catch(y){throw new Error(`Invalid custom RegExp for param "${O}" (${F}): `+y.message)}}let b=C?`((?:${F})(?:/(?:${F}))*)`:`(${F})`;f||(b=D&&c.length<2?`(?:/${b})`:"/"+b),D&&(b+="?"),o+=b,v+=20,D&&(v+=-8),C&&(v+=-20),F===".*"&&(v+=-50)}u.push(v)}s.push(u)}if(n.strict&&n.end){const c=s.length-1;s[c][s[c].length-1]+=.7000000000000001}n.strict||(o+="/?"),n.end?o+="$":n.strict&&!o.endsWith("/")&&(o+="(?:/|$)");const i=new RegExp(o,n.sensitive?"":"i");function l(c){const u=c.match(i),f={};if(!u)return null;for(let m=1;m<u.length;m++){const v=u[m]||"",O=r[m-1];f[O.name]=v&&O.repeatable?v.split("/"):v}return f}function a(c){let u="",f=!1;for(const m of e){(!f||!u.endsWith("/"))&&(u+="/"),f=!1;for(const v of m)if(v.type===0)u+=v.value;else if(v.type===1){const{value:O,repeatable:C,optional:D}=v,E=O in c?c[O]:"";if(_t(E)&&!C)throw new Error(`Provided param "${O}" is an array but it is not repeatable (* or + modifiers)`);const F=_t(E)?E.join("/"):E;if(!F)if(D)m.length<2&&(u.endsWith("/")?u=u.slice(0,-1):f=!0);else throw new Error(`Missing required param "${O}"`);u+=F}}return u||"/"}return{re:i,score:s,keys:r,parse:l,stringify:a}}function Uf(e,t){let n=0;for(;n<e.length&&n<t.length;){const s=t[n]-e[n];if(s)return s;n++}return e.length<t.length?e.length===1&&e[0]===80?-1:1:e.length>t.length?t.length===1&&t[0]===80?1:-1:0}function ta(e,t){let n=0;const s=e.score,o=t.score;for(;n<s.length&&n<o.length;){const r=Uf(s[n],o[n]);if(r)return r;n++}if(Math.abs(o.length-s.length)===1){if(Go(s))return 1;if(Go(o))return-1}return o.length-s.length}function Go(e){const t=e[e.length-1];return e.length>0&&t[t.length-1]<0}const Hf={type:0,value:""},Vf=/[a-zA-Z0-9_]/;function Wf(e){if(!e)return[[]];if(e==="/")return[[Hf]];if(!e.startsWith("/"))throw new Error(`Invalid path "${e}"`);function t(v){throw new Error(`ERR (${n})/"${c}": ${v}`)}let n=0,s=n;const o=[];let r;function i(){r&&o.push(r),r=[]}let l=0,a,c="",u="";function f(){c&&(n===0?r.push({type:0,value:c}):n===1||n===2||n===3?(r.length>1&&(a==="*"||a==="+")&&t(`A repeatable param (${c}) must be alone in its segment. eg: '/:ids+.`),r.push({type:1,value:c,regexp:u,repeatable:a==="*"||a==="+",optional:a==="*"||a==="?"})):t("Invalid state to consume buffer"),c="")}function m(){c+=a}for(;l<e.length;){if(a=e[l++],a==="\\"&&n!==2){s=n,n=4;continue}switch(n){case 0:a==="/"?(c&&f(),i()):a===":"?(f(),n=1):m();break;case 4:m(),n=s;break;case 1:a==="("?n=2:Vf.test(a)?m():(f(),n=0,a!=="*"&&a!=="?"&&a!=="+"&&l--);break;case 2:a===")"?u[u.length-1]=="\\"?u=u.slice(0,-1)+a:n=3:u+=a;break;case 3:f(),n=0,a!=="*"&&a!=="?"&&a!=="+"&&l--,u="";break;default:t("Unknown state");break}}return n===2&&t(`Unfinished custom RegExp for param "${c}"`),f(),i(),o}function jf(e,t,n){const s=$f(Wf(e.path),n),o=ce(s,{record:e,parent:t,children:[],alias:[]});return t&&!o.record.aliasOf==!t.record.aliasOf&&t.children.push(o),o}function Kf(e,t){const n=[],s=new Map;t=qo({strict:!1,end:!0,sensitive:!1},t);function o(f){return s.get(f)}function r(f,m,v){const O=!v,C=zo(f);C.aliasOf=v&&v.record;const D=qo(t,f),E=[C];if("alias"in f){const y=typeof f.alias=="string"?[f.alias]:f.alias;for(const R of y)E.push(zo(ce({},C,{components:v?v.record.components:C.components,path:R,aliasOf:v?v.record:C})))}let F,b;for(const y of E){const{path:R}=y;if(m&&R[0]!=="/"){const A=m.record.path,x=A[A.length-1]==="/"?"":"/";y.path=m.record.path+(R&&x+R)}if(F=jf(y,m,D),v?v.alias.push(F):(b=b||F,b!==F&&b.alias.push(F),O&&f.name&&!Xo(F)&&i(f.name)),na(F)&&a(F),C.children){const A=C.children;for(let x=0;x<A.length;x++)r(A[x],F,v&&v.children[x])}v=v||F}return b?()=>{i(b)}:Un}function i(f){if(Zl(f)){const m=s.get(f);m&&(s.delete(f),n.splice(n.indexOf(m),1),m.children.forEach(i),m.alias.forEach(i))}else{const m=n.indexOf(f);m>-1&&(n.splice(m,1),f.record.name&&s.delete(f.record.name),f.children.forEach(i),f.alias.forEach(i))}}function l(){return n}function a(f){const m=Yf(f,n);n.splice(m,0,f),f.record.name&&!Xo(f)&&s.set(f.record.name,f)}function c(f,m){let v,O={},C,D;if("name"in f&&f.name){if(v=s.get(f.name),!v)throw Sn(1,{location:f});D=v.record.name,O=ce(Yo(m.params,v.keys.filter(b=>!b.optional).concat(v.parent?v.parent.keys.filter(b=>b.optional):[]).map(b=>b.name)),f.params&&Yo(f.params,v.keys.map(b=>b.name))),C=v.stringify(O)}else if(f.path!=null)C=f.path,v=n.find(b=>b.re.test(C)),v&&(O=v.parse(C),D=v.record.name);else{if(v=m.name?s.get(m.name):n.find(b=>b.re.test(m.path)),!v)throw Sn(1,{location:f,currentLocation:m});D=v.record.name,O=ce({},m.params,f.params),C=v.stringify(O)}const E=[];let F=v;for(;F;)E.unshift(F.record),F=F.parent;return{name:D,path:C,params:O,matched:E,meta:Gf(E)}}e.forEach(f=>r(f));function u(){n.length=0,s.clear()}return{addRoute:r,resolve:c,removeRoute:i,clearRoutes:u,getRoutes:l,getRecordMatcher:o}}function Yo(e,t){const n={};for(const s of t)s in e&&(n[s]=e[s]);return n}function zo(e){const t={path:e.path,redirect:e.redirect,name:e.name,meta:e.meta||{},aliasOf:e.aliasOf,beforeEnter:e.beforeEnter,props:Bf(e),children:e.children||[],instances:{},leaveGuards:new Set,updateGuards:new Set,enterCallbacks:{},components:"components"in e?e.components||null:e.component&&{default:e.component}};return Object.defineProperty(t,"mods",{value:{}}),t}function Bf(e){const t={},n=e.props||!1;if("component"in e)t.default=n;else for(const s in e.components)t[s]=typeof n=="object"?n[s]:n;return t}function Xo(e){for(;e;){if(e.record.aliasOf)return!0;e=e.parent}return!1}function Gf(e){return e.reduce((t,n)=>ce(t,n.meta),{})}function qo(e,t){const n={};for(const s in e)n[s]=s in t?t[s]:e[s];return n}function Yf(e,t){let n=0,s=t.length;for(;n!==s;){const r=n+s>>1;ta(e,t[r])<0?s=r:n=r+1}const o=zf(e);return o&&(s=t.lastIndexOf(o,s-1)),s}function zf(e){let t=e;for(;t=t.parent;)if(na(t)&&ta(e,t)===0)return t}function na({record:e}){return!!(e.name||e.components&&Object.keys(e.components).length||e.redirect)}function Xf(e){const t={};if(e===""||e==="?")return t;const s=(e[0]==="?"?e.slice(1):e).split("&");for(let o=0;o<s.length;++o){const r=s[o].replace(Yl," "),i=r.indexOf("="),l=Yn(i<0?r:r.slice(0,i)),a=i<0?null:Yn(r.slice(i+1));if(l in t){let c=t[l];_t(c)||(c=t[l]=[c]),c.push(a)}else t[l]=a}return t}function Jo(e){let t="";for(let n in e){const s=e[n];if(n=_f(n),s==null){s!==void 0&&(t+=(t.length?"&":"")+n);continue}(_t(s)?s.map(r=>r&&Tr(r)):[s&&Tr(s)]).forEach(r=>{r!==void 0&&(t+=(t.length?"&":"")+n,r!=null&&(t+="="+r))})}return t}function qf(e){const t={};for(const n in e){const s=e[n];s!==void 0&&(t[n]=_t(s)?s.map(o=>o==null?null:""+o):s==null?s:""+s)}return t}const Jf=Symbol(""),Qo=Symbol(""),Zr=Symbol(""),sa=Symbol(""),Sr=Symbol("");function An(){let e=[];function t(s){return e.push(s),()=>{const o=e.indexOf(s);o>-1&&e.splice(o,1)}}function n(){e=[]}return{add:t,list:()=>e.slice(),reset:n}}function jt(e,t,n,s,o,r=i=>i()){const i=s&&(s.enterCallbacks[o]=s.enterCallbacks[o]||[]);return()=>new Promise((l,a)=>{const c=m=>{m===!1?a(Sn(4,{from:n,to:t})):m instanceof Error?a(m):Ff(m)?a(Sn(2,{from:t,to:m})):(i&&s.enterCallbacks[o]===i&&typeof m=="function"&&i.push(m),l())},u=r(()=>e.call(s&&s.instances[o],t,n,c));let f=Promise.resolve(u);e.length<3&&(f=f.then(c)),f.catch(m=>a(m))})}function lr(e,t,n,s,o=r=>r()){const r=[];for(const i of e)for(const l in i.components){let a=i.components[l];if(!(t!=="beforeRouteEnter"&&!i.instances[l]))if(Bl(a)){const u=(a.__vccOpts||a)[t];u&&r.push(jt(u,n,s,i,l,o))}else{let c=a();r.push(()=>c.then(u=>{if(!u)throw new Error(`Couldn't resolve component "${l}" at "${i.path}"`);const f=sf(u)?u.default:u;i.mods[l]=u,i.components[l]=f;const v=(f.__vccOpts||f)[t];return v&&jt(v,n,s,i,l,o)()}))}}return r}function Zo(e){const t=It(Zr),n=It(sa),s=Ee(()=>{const a=Le(e.to);return t.resolve(a)}),o=Ee(()=>{const{matched:a}=s.value,{length:c}=a,u=a[c-1],f=n.matched;if(!u||!f.length)return-1;const m=f.findIndex(In.bind(null,u));if(m>-1)return m;const v=ei(a[c-2]);return c>1&&ei(u)===v&&f[f.length-1].path!==v?f.findIndex(In.bind(null,a[c-2])):m}),r=Ee(()=>o.value>-1&&td(n.params,s.value.params)),i=Ee(()=>o.value>-1&&o.value===n.matched.length-1&&Jl(n.params,s.value.params));function l(a={}){if(ed(a)){const c=t[Le(e.replace)?"replace":"push"](Le(e.to)).catch(Un);return e.viewTransition&&typeof document<"u"&&"startViewTransition"in document&&document.startViewTransition(()=>c),c}return Promise.resolve()}return{route:s,href:Ee(()=>s.value.href),isActive:r,isExactActive:i,navigate:l}}function Qf(e){return e.length===1?e[0]:e}const Zf=Ze({name:"RouterLink",compatConfig:{MODE:3},props:{to:{type:[String,Object],required:!0},replace:Boolean,activeClass:String,exactActiveClass:String,custom:Boolean,ariaCurrentValue:{type:String,default:"page"},viewTransition:Boolean},useLink:Zo,setup(e,{slots:t}){const n=Ds(Zo(e)),{options:s}=It(Zr),o=Ee(()=>({[ti(e.activeClass,s.linkActiveClass,"router-link-active")]:n.isActive,[ti(e.exactActiveClass,s.linkExactActiveClass,"router-link-exact-active")]:n.isExactActive}));return()=>{const r=t.default&&Qf(t.default(n));return e.custom?r:Vs("a",{"aria-current":n.isExactActive?e.ariaCurrentValue:null,href:n.href,onClick:n.navigate,class:o.value},r)}}}),Ue=Zf;function ed(e){if(!(e.metaKey||e.altKey||e.ctrlKey||e.shiftKey)&&!e.defaultPrevented&&!(e.button!==void 0&&e.button!==0)){if(e.currentTarget&&e.currentTarget.getAttribute){const t=e.currentTarget.getAttribute("target");if(/\b_blank\b/i.test(t))return}return e.preventDefault&&e.preventDefault(),!0}}function td(e,t){for(const n in t){const s=t[n],o=e[n];if(typeof s=="string"){if(s!==o)return!1}else if(!_t(o)||o.length!==s.length||s.some((r,i)=>r!==o[i]))return!1}return!0}function ei(e){return e?e.aliasOf?e.aliasOf.path:e.path:""}const ti=(e,t,n)=>e??t??n,nd=Ze({name:"RouterView",inheritAttrs:!1,props:{name:{type:String,default:"default"},route:Object},compatConfig:{MODE:3},setup(e,{attrs:t,slots:n}){const s=It(Sr),o=Ee(()=>e.route||s.value),r=It(Qo,0),i=Ee(()=>{let c=Le(r);const{matched:u}=o.value;let f;for(;(f=u[c])&&!f.components;)c++;return c}),l=Ee(()=>o.value.matched[i.value]);fs(Qo,Ee(()=>i.value+1)),fs(Jf,l),fs(Sr,o);const a=Ve();return ln(()=>[a.value,l.value,e.name],([c,u,f],[m,v,O])=>{u&&(u.instances[f]=c,v&&v!==u&&c&&c===m&&(u.leaveGuards.size||(u.leaveGuards=v.leaveGuards),u.updateGuards.size||(u.updateGuards=v.updateGuards))),c&&u&&(!v||!In(u,v)||!m)&&(u.enterCallbacks[f]||[]).forEach(C=>C(c))},{flush:"post"}),()=>{const c=o.value,u=e.name,f=l.value,m=f&&f.components[u];if(!m)return ni(n.default,{Component:m,route:c});const v=f.props[u],O=v?v===!0?c.params:typeof v=="function"?v(c):v:null,D=Vs(m,ce({},O,t,{onVnodeUnmounted:E=>{E.component.isUnmounted&&(f.instances[u]=null)},ref:a}));return ni(n.default,{Component:D,route:c})||D}}});function ni(e,t){if(!e)return null;const n=e(t);return n.length===1?n[0]:n}const ra=nd;function sd(e){const t=Kf(e.routes,e),n=e.parseQuery||Xf,s=e.stringifyQuery||Jo,o=e.history,r=An(),i=An(),l=An(),a=Br(Ut);let c=Ut;hn&&e.scrollBehavior&&"scrollRestoration"in history&&(history.scrollRestoration="manual");const u=or.bind(null,w=>""+w),f=or.bind(null,gf),m=or.bind(null,Yn);function v(w,G){let j,Y;return Zl(w)?(j=t.getRecordMatcher(w),Y=G):Y=w,t.addRoute(Y,j)}function O(w){const G=t.getRecordMatcher(w);G&&t.removeRoute(G)}function C(){return t.getRoutes().map(w=>w.record)}function D(w){return!!t.getRecordMatcher(w)}function E(w,G){if(G=ce({},G||a.value),typeof w=="string"){const _=ir(n,w,G.path),S=t.resolve({path:_.path},G),M=o.createHref(_.fullPath);return ce(_,S,{params:m(S.params),hash:Yn(_.hash),redirectedFrom:void 0,href:M})}let j;if(w.path!=null)j=ce({},w,{path:ir(n,w.path,G.path).path});else{const _=ce({},w.params);for(const S in _)_[S]==null&&delete _[S];j=ce({},w,{params:f(_)}),G.params=f(G.params)}const Y=t.resolve(j,G),ie=w.hash||"";Y.params=u(m(Y.params));const p=yf(s,ce({},w,{hash:mf(ie),path:Y.path})),g=o.createHref(p);return ce({fullPath:p,hash:ie,query:s===Jo?qf(w.query):w.query||{}},Y,{redirectedFrom:void 0,href:g})}function F(w){return typeof w=="string"?ir(n,w,a.value.path):ce({},w)}function b(w,G){if(c!==w)return Sn(8,{from:G,to:w})}function y(w){return x(w)}function R(w){return y(ce(F(w),{replace:!0}))}function A(w){const G=w.matched[w.matched.length-1];if(G&&G.redirect){const{redirect:j}=G;let Y=typeof j=="function"?j(w):j;return typeof Y=="string"&&(Y=Y.includes("?")||Y.includes("#")?Y=F(Y):{path:Y},Y.params={}),ce({query:w.query,hash:w.hash,params:Y.path!=null?{}:w.params},Y)}}function x(w,G){const j=c=E(w),Y=a.value,ie=w.state,p=w.force,g=w.replace===!0,_=A(j);if(_)return x(ce(F(_),{state:typeof _=="object"?ce({},ie,_.state):ie,force:p,replace:g}),G||j);const S=j;S.redirectedFrom=G;let M;return!p&&Ef(s,Y,j)&&(M=Sn(16,{to:S,from:Y}),Ie(Y,Y,!0,!1)),(M?Promise.resolve(M):ae(S,Y)).catch(k=>Pt(k)?Pt(k,2)?k:qe(k):se(k,S,Y)).then(k=>{if(k){if(Pt(k,2))return x(ce({replace:g},F(k.to),{state:typeof k.to=="object"?ce({},ie,k.to.state):ie,force:p}),G||S)}else k=ee(S,Y,!0,g,ie);return Te(S,Y,k),k})}function B(w,G){const j=b(w,G);return j?Promise.reject(j):Promise.resolve()}function H(w){const G=et.values().next().value;return G&&typeof G.runWithContext=="function"?G.runWithContext(w):w()}function ae(w,G){let j;const[Y,ie,p]=rd(w,G);j=lr(Y.reverse(),"beforeRouteLeave",w,G);for(const _ of Y)_.leaveGuards.forEach(S=>{j.push(jt(S,w,G))});const g=B.bind(null,w,G);return j.push(g),Ae(j).then(()=>{j=[];for(const _ of r.list())j.push(jt(_,w,G));return j.push(g),Ae(j)}).then(()=>{j=lr(ie,"beforeRouteUpdate",w,G);for(const _ of ie)_.updateGuards.forEach(S=>{j.push(jt(S,w,G))});return j.push(g),Ae(j)}).then(()=>{j=[];for(const _ of p)if(_.beforeEnter)if(_t(_.beforeEnter))for(const S of _.beforeEnter)j.push(jt(S,w,G));else j.push(jt(_.beforeEnter,w,G));return j.push(g),Ae(j)}).then(()=>(w.matched.forEach(_=>_.enterCallbacks={}),j=lr(p,"beforeRouteEnter",w,G,H),j.push(g),Ae(j))).then(()=>{j=[];for(const _ of i.list())j.push(jt(_,w,G));return j.push(g),Ae(j)}).catch(_=>Pt(_,8)?_:Promise.reject(_))}function Te(w,G,j){l.list().forEach(Y=>H(()=>Y(w,G,j)))}function ee(w,G,j,Y,ie){const p=b(w,G);if(p)return p;const g=G===Ut,_=hn?history.state:{};j&&(Y||g?o.replace(w.fullPath,ce({scroll:g&&_&&_.scroll},ie)):o.push(w.fullPath,ie)),a.value=w,Ie(w,G,j,g),qe()}let me;function ut(){me||(me=o.listen((w,G,j)=>{if(!pt.listening)return;const Y=E(w),ie=A(Y);if(ie){x(ce(ie,{replace:!0,force:!0}),Y).catch(Un);return}c=Y;const p=a.value;hn&&Nf(Wo(p.fullPath,j.delta),Ws()),ae(Y,p).catch(g=>Pt(g,12)?g:Pt(g,2)?(x(ce(F(g.to),{force:!0}),Y).then(_=>{Pt(_,20)&&!j.delta&&j.type===zn.pop&&o.go(-1,!1)}).catch(Un),Promise.reject()):(j.delta&&o.go(-j.delta,!1),se(g,Y,p))).then(g=>{g=g||ee(Y,p,!1),g&&(j.delta&&!Pt(g,8)?o.go(-j.delta,!1):j.type===zn.pop&&Pt(g,20)&&o.go(-1,!1)),Te(Y,p,g)}).catch(Un)}))}let ot=An(),de=An(),oe;function se(w,G,j){qe(w);const Y=de.list();return Y.length?Y.forEach(ie=>ie(w,G,j)):console.error(w),Promise.reject(w)}function Ke(){return oe&&a.value!==Ut?Promise.resolve():new Promise((w,G)=>{ot.add([w,G])})}function qe(w){return oe||(oe=!w,ut(),ot.list().forEach(([G,j])=>w?j(w):G()),ot.reset()),w}function Ie(w,G,j,Y){const{scrollBehavior:ie}=e;if(!hn||!ie)return Promise.resolve();const p=!j&&Af(Wo(w.fullPath,0))||(Y||!j)&&history.state&&history.state.scroll||null;return Gr().then(()=>ie(w,G,p)).then(g=>g&&Pf(g)).catch(g=>se(g,w,G))}const Se=w=>o.go(w);let it;const et=new Set,pt={currentRoute:a,listening:!0,addRoute:v,removeRoute:O,clearRoutes:t.clearRoutes,hasRoute:D,getRoutes:C,resolve:E,options:e,push:y,replace:R,go:Se,back:()=>Se(-1),forward:()=>Se(1),beforeEach:r.add,beforeResolve:i.add,afterEach:l.add,onError:de.add,isReady:Ke,install(w){const G=this;w.component("RouterLink",Ue),w.component("RouterView",ra),w.config.globalProperties.$router=G,Object.defineProperty(w.config.globalProperties,"$route",{enumerable:!0,get:()=>Le(a)}),hn&&!it&&a.value===Ut&&(it=!0,y(o.location).catch(ie=>{}));const j={};for(const ie in Ut)Object.defineProperty(j,ie,{get:()=>a.value[ie],enumerable:!0});w.provide(Zr,G),w.provide(sa,cl(j)),w.provide(Sr,a);const Y=w.unmount;et.add(w),w.unmount=function(){et.delete(w),et.size<1&&(c=Ut,me&&me(),me=null,a.value=Ut,it=!1,oe=!1),Y()}}};function Ae(w){return w.reduce((G,j)=>G.then(()=>H(j)),Promise.resolve())}return pt}function rd(e,t){const n=[],s=[],o=[],r=Math.max(t.matched.length,e.matched.length);for(let i=0;i<r;i++){const l=t.matched[i];l&&(e.matched.find(c=>In(c,l))?s.push(l):n.push(l));const a=e.matched[i];a&&(t.matched.find(c=>In(c,a))||o.push(a))}return[n,s,o]}/*!
  * shared v9.14.4
  * (c) 2025 kazuya kawaguchi
  * Released under the MIT License.
  */const Ps=typeof window<"u",Xt=(e,t=!1)=>t?Symbol.for(e):Symbol(e),od=(e,t,n)=>id({l:e,k:t,s:n}),id=e=>JSON.stringify(e).replace(/\u2028/g,"\\u2028").replace(/\u2029/g,"\\u2029").replace(/\u0027/g,"\\u0027"),Ce=e=>typeof e=="number"&&isFinite(e),ld=e=>ia(e)==="[object Date]",zt=e=>ia(e)==="[object RegExp]",js=e=>Q(e)&&Object.keys(e).length===0,De=Object.assign,ad=Object.create,he=(e=null)=>ad(e);let si;const Mt=()=>si||(si=typeof globalThis<"u"?globalThis:typeof self<"u"?self:typeof window<"u"?window:typeof global<"u"?global:he());function ri(e){return e.replace(/</g,"&lt;").replace(/>/g,"&gt;").replace(/"/g,"&quot;").replace(/'/g,"&apos;")}const cd=Object.prototype.hasOwnProperty;function dt(e,t){return cd.call(e,t)}const be=Array.isArray,ge=e=>typeof e=="function",K=e=>typeof e=="string",ne=e=>typeof e=="boolean",le=e=>e!==null&&typeof e=="object",ud=e=>le(e)&&ge(e.then)&&ge(e.catch),oa=Object.prototype.toString,ia=e=>oa.call(e),Q=e=>{if(!le(e))return!1;const t=Object.getPrototypeOf(e);return t===null||t.constructor===Object},fd=e=>e==null?"":be(e)||Q(e)&&e.toString===oa?JSON.stringify(e,null,2):String(e);function dd(e,t=""){return e.reduce((n,s,o)=>o===0?n+s:n+t+s,"")}function Ks(e){let t=e;return()=>++t}function hd(e,t){typeof console<"u"&&(console.warn("[intlify] "+e),t&&console.warn(t.stack))}const is=e=>!le(e)||be(e);function _s(e,t){if(is(e)||is(t))throw new Error("Invalid value");const n=[{src:e,des:t}];for(;n.length;){const{src:s,des:o}=n.pop();Object.keys(s).forEach(r=>{r!=="__proto__"&&(le(s[r])&&!le(o[r])&&(o[r]=Array.isArray(s[r])?[]:he()),is(o[r])||is(s[r])?o[r]=s[r]:n.push({src:s[r],des:o[r]}))})}}/*!
  * message-compiler v9.14.4
  * (c) 2025 kazuya kawaguchi
  * Released under the MIT License.
  */function md(e,t,n){return{line:e,column:t,offset:n}}function Ns(e,t,n){return{start:e,end:t}}const _d=/\{([0-9a-zA-Z]+)\}/g;function la(e,...t){return t.length===1&&pd(t[0])&&(t=t[0]),(!t||!t.hasOwnProperty)&&(t={}),e.replace(_d,(n,s)=>t.hasOwnProperty(s)?t[s]:"")}const aa=Object.assign,oi=e=>typeof e=="string",pd=e=>e!==null&&typeof e=="object";function ca(e,t=""){return e.reduce((n,s,o)=>o===0?n+s:n+t+s,"")}const eo={USE_MODULO_SYNTAX:1,__EXTEND_POINT__:2},gd={[eo.USE_MODULO_SYNTAX]:"Use modulo before '{{0}}'."};function vd(e,t,...n){const s=la(gd[e],...n||[]),o={message:String(s),code:e};return t&&(o.location=t),o}const q={EXPECTED_TOKEN:1,INVALID_TOKEN_IN_PLACEHOLDER:2,UNTERMINATED_SINGLE_QUOTE_IN_PLACEHOLDER:3,UNKNOWN_ESCAPE_SEQUENCE:4,INVALID_UNICODE_ESCAPE_SEQUENCE:5,UNBALANCED_CLOSING_BRACE:6,UNTERMINATED_CLOSING_BRACE:7,EMPTY_PLACEHOLDER:8,NOT_ALLOW_NEST_PLACEHOLDER:9,INVALID_LINKED_FORMAT:10,MUST_HAVE_MESSAGES_IN_PLURAL:11,UNEXPECTED_EMPTY_LINKED_MODIFIER:12,UNEXPECTED_EMPTY_LINKED_KEY:13,UNEXPECTED_LEXICAL_ANALYSIS:14,UNHANDLED_CODEGEN_NODE_TYPE:15,UNHANDLED_MINIFIER_NODE_TYPE:16,__EXTEND_POINT__:17},bd={[q.EXPECTED_TOKEN]:"Expected token: '{0}'",[q.INVALID_TOKEN_IN_PLACEHOLDER]:"Invalid token in placeholder: '{0}'",[q.UNTERMINATED_SINGLE_QUOTE_IN_PLACEHOLDER]:"Unterminated single quote in placeholder",[q.UNKNOWN_ESCAPE_SEQUENCE]:"Unknown escape sequence: \\{0}",[q.INVALID_UNICODE_ESCAPE_SEQUENCE]:"Invalid unicode escape sequence: {0}",[q.UNBALANCED_CLOSING_BRACE]:"Unbalanced closing brace",[q.UNTERMINATED_CLOSING_BRACE]:"Unterminated closing brace",[q.EMPTY_PLACEHOLDER]:"Empty placeholder",[q.NOT_ALLOW_NEST_PLACEHOLDER]:"Not allowed nest placeholder",[q.INVALID_LINKED_FORMAT]:"Invalid linked format",[q.MUST_HAVE_MESSAGES_IN_PLURAL]:"Plural must have messages",[q.UNEXPECTED_EMPTY_LINKED_MODIFIER]:"Unexpected empty linked modifier",[q.UNEXPECTED_EMPTY_LINKED_KEY]:"Unexpected empty linked key",[q.UNEXPECTED_LEXICAL_ANALYSIS]:"Unexpected lexical analysis in token: '{0}'",[q.UNHANDLED_CODEGEN_NODE_TYPE]:"unhandled codegen node type: '{0}'",[q.UNHANDLED_MINIFIER_NODE_TYPE]:"unhandled mimifier node type: '{0}'"};function On(e,t,n={}){const{domain:s,messages:o,args:r}=n,i=la((o||bd)[e]||"",...r||[]),l=new SyntaxError(String(i));return l.code=e,t&&(l.location=t),l.domain=s,l}function yd(e){throw e}const Nt=" ",Ed="\r",Ye=`
`,Td="\u2028",Id="\u2029";function Sd(e){const t=e;let n=0,s=1,o=1,r=0;const i=x=>t[x]===Ed&&t[x+1]===Ye,l=x=>t[x]===Ye,a=x=>t[x]===Id,c=x=>t[x]===Td,u=x=>i(x)||l(x)||a(x)||c(x),f=()=>n,m=()=>s,v=()=>o,O=()=>r,C=x=>i(x)||a(x)||c(x)?Ye:t[x],D=()=>C(n),E=()=>C(n+r);function F(){return r=0,u(n)&&(s++,o=0),i(n)&&n++,n++,o++,t[n]}function b(){return i(n+r)&&r++,r++,t[n+r]}function y(){n=0,s=1,o=1,r=0}function R(x=0){r=x}function A(){const x=n+r;for(;x!==n;)F();r=0}return{index:f,line:m,column:v,peekOffset:O,charAt:C,currentChar:D,currentPeek:E,next:F,peek:b,reset:y,resetPeek:R,skipToPeek:A}}const Ht=void 0,Ld=".",ii="'",Cd="tokenizer";function Od(e,t={}){const n=t.location!==!1,s=Sd(e),o=()=>s.index(),r=()=>md(s.line(),s.column(),s.index()),i=r(),l=o(),a={currentType:14,offset:l,startLoc:i,endLoc:i,lastType:14,lastOffset:l,lastStartLoc:i,lastEndLoc:i,braceNest:0,inLinked:!1,text:""},c=()=>a,{onError:u}=t;function f(d,h,I,...P){const W=c();if(h.column+=I,h.offset+=I,u){const $=n?Ns(W.startLoc,h):null,T=On(d,$,{domain:Cd,args:P});u(T)}}function m(d,h,I){d.endLoc=r(),d.currentType=h;const P={type:h};return n&&(P.loc=Ns(d.startLoc,d.endLoc)),I!=null&&(P.value=I),P}const v=d=>m(d,14);function O(d,h){return d.currentChar()===h?(d.next(),h):(f(q.EXPECTED_TOKEN,r(),0,h),"")}function C(d){let h="";for(;d.currentPeek()===Nt||d.currentPeek()===Ye;)h+=d.currentPeek(),d.peek();return h}function D(d){const h=C(d);return d.skipToPeek(),h}function E(d){if(d===Ht)return!1;const h=d.charCodeAt(0);return h>=97&&h<=122||h>=65&&h<=90||h===95}function F(d){if(d===Ht)return!1;const h=d.charCodeAt(0);return h>=48&&h<=57}function b(d,h){const{currentType:I}=h;if(I!==2)return!1;C(d);const P=E(d.currentPeek());return d.resetPeek(),P}function y(d,h){const{currentType:I}=h;if(I!==2)return!1;C(d);const P=d.currentPeek()==="-"?d.peek():d.currentPeek(),W=F(P);return d.resetPeek(),W}function R(d,h){const{currentType:I}=h;if(I!==2)return!1;C(d);const P=d.currentPeek()===ii;return d.resetPeek(),P}function A(d,h){const{currentType:I}=h;if(I!==8)return!1;C(d);const P=d.currentPeek()===".";return d.resetPeek(),P}function x(d,h){const{currentType:I}=h;if(I!==9)return!1;C(d);const P=E(d.currentPeek());return d.resetPeek(),P}function B(d,h){const{currentType:I}=h;if(!(I===8||I===12))return!1;C(d);const P=d.currentPeek()===":";return d.resetPeek(),P}function H(d,h){const{currentType:I}=h;if(I!==10)return!1;const P=()=>{const $=d.currentPeek();return $==="{"?E(d.peek()):$==="@"||$==="%"||$==="|"||$===":"||$==="."||$===Nt||!$?!1:$===Ye?(d.peek(),P()):ee(d,!1)},W=P();return d.resetPeek(),W}function ae(d){C(d);const h=d.currentPeek()==="|";return d.resetPeek(),h}function Te(d){const h=C(d),I=d.currentPeek()==="%"&&d.peek()==="{";return d.resetPeek(),{isModulo:I,hasSpace:h.length>0}}function ee(d,h=!0){const I=(W=!1,$="",T=!1)=>{const N=d.currentPeek();return N==="{"?$==="%"?!1:W:N==="@"||!N?$==="%"?!0:W:N==="%"?(d.peek(),I(W,"%",!0)):N==="|"?$==="%"||T?!0:!($===Nt||$===Ye):N===Nt?(d.peek(),I(!0,Nt,T)):N===Ye?(d.peek(),I(!0,Ye,T)):!0},P=I();return h&&d.resetPeek(),P}function me(d,h){const I=d.currentChar();return I===Ht?Ht:h(I)?(d.next(),I):null}function ut(d){const h=d.charCodeAt(0);return h>=97&&h<=122||h>=65&&h<=90||h>=48&&h<=57||h===95||h===36}function ot(d){return me(d,ut)}function de(d){const h=d.charCodeAt(0);return h>=97&&h<=122||h>=65&&h<=90||h>=48&&h<=57||h===95||h===36||h===45}function oe(d){return me(d,de)}function se(d){const h=d.charCodeAt(0);return h>=48&&h<=57}function Ke(d){return me(d,se)}function qe(d){const h=d.charCodeAt(0);return h>=48&&h<=57||h>=65&&h<=70||h>=97&&h<=102}function Ie(d){return me(d,qe)}function Se(d){let h="",I="";for(;h=Ke(d);)I+=h;return I}function it(d){D(d);const h=d.currentChar();return h!=="%"&&f(q.EXPECTED_TOKEN,r(),0,h),d.next(),"%"}function et(d){let h="";for(;;){const I=d.currentChar();if(I==="{"||I==="}"||I==="@"||I==="|"||!I)break;if(I==="%")if(ee(d))h+=I,d.next();else break;else if(I===Nt||I===Ye)if(ee(d))h+=I,d.next();else{if(ae(d))break;h+=I,d.next()}else h+=I,d.next()}return h}function pt(d){D(d);let h="",I="";for(;h=oe(d);)I+=h;return d.currentChar()===Ht&&f(q.UNTERMINATED_CLOSING_BRACE,r(),0),I}function Ae(d){D(d);let h="";return d.currentChar()==="-"?(d.next(),h+=`-${Se(d)}`):h+=Se(d),d.currentChar()===Ht&&f(q.UNTERMINATED_CLOSING_BRACE,r(),0),h}function w(d){return d!==ii&&d!==Ye}function G(d){D(d),O(d,"'");let h="",I="";for(;h=me(d,w);)h==="\\"?I+=j(d):I+=h;const P=d.currentChar();return P===Ye||P===Ht?(f(q.UNTERMINATED_SINGLE_QUOTE_IN_PLACEHOLDER,r(),0),P===Ye&&(d.next(),O(d,"'")),I):(O(d,"'"),I)}function j(d){const h=d.currentChar();switch(h){case"\\":case"'":return d.next(),`\\${h}`;case"u":return Y(d,h,4);case"U":return Y(d,h,6);default:return f(q.UNKNOWN_ESCAPE_SEQUENCE,r(),0,h),""}}function Y(d,h,I){O(d,h);let P="";for(let W=0;W<I;W++){const $=Ie(d);if(!$){f(q.INVALID_UNICODE_ESCAPE_SEQUENCE,r(),0,`\\${h}${P}${d.currentChar()}`);break}P+=$}return`\\${h}${P}`}function ie(d){return d!=="{"&&d!=="}"&&d!==Nt&&d!==Ye}function p(d){D(d);let h="",I="";for(;h=me(d,ie);)I+=h;return I}function g(d){let h="",I="";for(;h=ot(d);)I+=h;return I}function _(d){const h=I=>{const P=d.currentChar();return P==="{"||P==="%"||P==="@"||P==="|"||P==="("||P===")"||!P||P===Nt?I:(I+=P,d.next(),h(I))};return h("")}function S(d){D(d);const h=O(d,"|");return D(d),h}function M(d,h){let I=null;switch(d.currentChar()){case"{":return h.braceNest>=1&&f(q.NOT_ALLOW_NEST_PLACEHOLDER,r(),0),d.next(),I=m(h,2,"{"),D(d),h.braceNest++,I;case"}":return h.braceNest>0&&h.currentType===2&&f(q.EMPTY_PLACEHOLDER,r(),0),d.next(),I=m(h,3,"}"),h.braceNest--,h.braceNest>0&&D(d),h.inLinked&&h.braceNest===0&&(h.inLinked=!1),I;case"@":return h.braceNest>0&&f(q.UNTERMINATED_CLOSING_BRACE,r(),0),I=k(d,h)||v(h),h.braceNest=0,I;default:{let W=!0,$=!0,T=!0;if(ae(d))return h.braceNest>0&&f(q.UNTERMINATED_CLOSING_BRACE,r(),0),I=m(h,1,S(d)),h.braceNest=0,h.inLinked=!1,I;if(h.braceNest>0&&(h.currentType===5||h.currentType===6||h.currentType===7))return f(q.UNTERMINATED_CLOSING_BRACE,r(),0),h.braceNest=0,V(d,h);if(W=b(d,h))return I=m(h,5,pt(d)),D(d),I;if($=y(d,h))return I=m(h,6,Ae(d)),D(d),I;if(T=R(d,h))return I=m(h,7,G(d)),D(d),I;if(!W&&!$&&!T)return I=m(h,13,p(d)),f(q.INVALID_TOKEN_IN_PLACEHOLDER,r(),0,I.value),D(d),I;break}}return I}function k(d,h){const{currentType:I}=h;let P=null;const W=d.currentChar();switch((I===8||I===9||I===12||I===10)&&(W===Ye||W===Nt)&&f(q.INVALID_LINKED_FORMAT,r(),0),W){case"@":return d.next(),P=m(h,8,"@"),h.inLinked=!0,P;case".":return D(d),d.next(),m(h,9,".");case":":return D(d),d.next(),m(h,10,":");default:return ae(d)?(P=m(h,1,S(d)),h.braceNest=0,h.inLinked=!1,P):A(d,h)||B(d,h)?(D(d),k(d,h)):x(d,h)?(D(d),m(h,12,g(d))):H(d,h)?(D(d),W==="{"?M(d,h)||P:m(h,11,_(d))):(I===8&&f(q.INVALID_LINKED_FORMAT,r(),0),h.braceNest=0,h.inLinked=!1,V(d,h))}}function V(d,h){let I={type:14};if(h.braceNest>0)return M(d,h)||v(h);if(h.inLinked)return k(d,h)||v(h);switch(d.currentChar()){case"{":return M(d,h)||v(h);case"}":return f(q.UNBALANCED_CLOSING_BRACE,r(),0),d.next(),m(h,3,"}");case"@":return k(d,h)||v(h);default:{if(ae(d))return I=m(h,1,S(d)),h.braceNest=0,h.inLinked=!1,I;const{isModulo:W,hasSpace:$}=Te(d);if(W)return $?m(h,0,et(d)):m(h,4,it(d));if(ee(d))return m(h,0,et(d));break}}return I}function U(){const{currentType:d,offset:h,startLoc:I,endLoc:P}=a;return a.lastType=d,a.lastOffset=h,a.lastStartLoc=I,a.lastEndLoc=P,a.offset=o(),a.startLoc=r(),s.currentChar()===Ht?m(a,14):V(s,a)}return{nextToken:U,currentOffset:o,currentPosition:r,context:c}}const Pd="parser",Nd=/(?:\\\\|\\'|\\u([0-9a-fA-F]{4})|\\U([0-9a-fA-F]{6}))/g;function Ad(e,t,n){switch(e){case"\\\\":return"\\";case"\\'":return"'";default:{const s=parseInt(t||n,16);return s<=55295||s>=57344?String.fromCodePoint(s):"�"}}}function Rd(e={}){const t=e.location!==!1,{onError:n,onWarn:s}=e;function o(b,y,R,A,...x){const B=b.currentPosition();if(B.offset+=A,B.column+=A,n){const H=t?Ns(R,B):null,ae=On(y,H,{domain:Pd,args:x});n(ae)}}function r(b,y,R,A,...x){const B=b.currentPosition();if(B.offset+=A,B.column+=A,s){const H=t?Ns(R,B):null;s(vd(y,H,x))}}function i(b,y,R){const A={type:b};return t&&(A.start=y,A.end=y,A.loc={start:R,end:R}),A}function l(b,y,R,A){t&&(b.end=y,b.loc&&(b.loc.end=R))}function a(b,y){const R=b.context(),A=i(3,R.offset,R.startLoc);return A.value=y,l(A,b.currentOffset(),b.currentPosition()),A}function c(b,y){const R=b.context(),{lastOffset:A,lastStartLoc:x}=R,B=i(5,A,x);return B.index=parseInt(y,10),b.nextToken(),l(B,b.currentOffset(),b.currentPosition()),B}function u(b,y,R){const A=b.context(),{lastOffset:x,lastStartLoc:B}=A,H=i(4,x,B);return H.key=y,R===!0&&(H.modulo=!0),b.nextToken(),l(H,b.currentOffset(),b.currentPosition()),H}function f(b,y){const R=b.context(),{lastOffset:A,lastStartLoc:x}=R,B=i(9,A,x);return B.value=y.replace(Nd,Ad),b.nextToken(),l(B,b.currentOffset(),b.currentPosition()),B}function m(b){const y=b.nextToken(),R=b.context(),{lastOffset:A,lastStartLoc:x}=R,B=i(8,A,x);return y.type!==12?(o(b,q.UNEXPECTED_EMPTY_LINKED_MODIFIER,R.lastStartLoc,0),B.value="",l(B,A,x),{nextConsumeToken:y,node:B}):(y.value==null&&o(b,q.UNEXPECTED_LEXICAL_ANALYSIS,R.lastStartLoc,0,ft(y)),B.value=y.value||"",l(B,b.currentOffset(),b.currentPosition()),{node:B})}function v(b,y){const R=b.context(),A=i(7,R.offset,R.startLoc);return A.value=y,l(A,b.currentOffset(),b.currentPosition()),A}function O(b){const y=b.context(),R=i(6,y.offset,y.startLoc);let A=b.nextToken();if(A.type===9){const x=m(b);R.modifier=x.node,A=x.nextConsumeToken||b.nextToken()}switch(A.type!==10&&o(b,q.UNEXPECTED_LEXICAL_ANALYSIS,y.lastStartLoc,0,ft(A)),A=b.nextToken(),A.type===2&&(A=b.nextToken()),A.type){case 11:A.value==null&&o(b,q.UNEXPECTED_LEXICAL_ANALYSIS,y.lastStartLoc,0,ft(A)),R.key=v(b,A.value||"");break;case 5:A.value==null&&o(b,q.UNEXPECTED_LEXICAL_ANALYSIS,y.lastStartLoc,0,ft(A)),R.key=u(b,A.value||"");break;case 6:A.value==null&&o(b,q.UNEXPECTED_LEXICAL_ANALYSIS,y.lastStartLoc,0,ft(A)),R.key=c(b,A.value||"");break;case 7:A.value==null&&o(b,q.UNEXPECTED_LEXICAL_ANALYSIS,y.lastStartLoc,0,ft(A)),R.key=f(b,A.value||"");break;default:{o(b,q.UNEXPECTED_EMPTY_LINKED_KEY,y.lastStartLoc,0);const x=b.context(),B=i(7,x.offset,x.startLoc);return B.value="",l(B,x.offset,x.startLoc),R.key=B,l(R,x.offset,x.startLoc),{nextConsumeToken:A,node:R}}}return l(R,b.currentOffset(),b.currentPosition()),{node:R}}function C(b){const y=b.context(),R=y.currentType===1?b.currentOffset():y.offset,A=y.currentType===1?y.endLoc:y.startLoc,x=i(2,R,A);x.items=[];let B=null,H=null;do{const ee=B||b.nextToken();switch(B=null,ee.type){case 0:ee.value==null&&o(b,q.UNEXPECTED_LEXICAL_ANALYSIS,y.lastStartLoc,0,ft(ee)),x.items.push(a(b,ee.value||""));break;case 6:ee.value==null&&o(b,q.UNEXPECTED_LEXICAL_ANALYSIS,y.lastStartLoc,0,ft(ee)),x.items.push(c(b,ee.value||""));break;case 4:H=!0;break;case 5:ee.value==null&&o(b,q.UNEXPECTED_LEXICAL_ANALYSIS,y.lastStartLoc,0,ft(ee)),x.items.push(u(b,ee.value||"",!!H)),H&&(r(b,eo.USE_MODULO_SYNTAX,y.lastStartLoc,0,ft(ee)),H=null);break;case 7:ee.value==null&&o(b,q.UNEXPECTED_LEXICAL_ANALYSIS,y.lastStartLoc,0,ft(ee)),x.items.push(f(b,ee.value||""));break;case 8:{const me=O(b);x.items.push(me.node),B=me.nextConsumeToken||null;break}}}while(y.currentType!==14&&y.currentType!==1);const ae=y.currentType===1?y.lastOffset:b.currentOffset(),Te=y.currentType===1?y.lastEndLoc:b.currentPosition();return l(x,ae,Te),x}function D(b,y,R,A){const x=b.context();let B=A.items.length===0;const H=i(1,y,R);H.cases=[],H.cases.push(A);do{const ae=C(b);B||(B=ae.items.length===0),H.cases.push(ae)}while(x.currentType!==14);return B&&o(b,q.MUST_HAVE_MESSAGES_IN_PLURAL,R,0),l(H,b.currentOffset(),b.currentPosition()),H}function E(b){const y=b.context(),{offset:R,startLoc:A}=y,x=C(b);return y.currentType===14?x:D(b,R,A,x)}function F(b){const y=Od(b,aa({},e)),R=y.context(),A=i(0,R.offset,R.startLoc);return t&&A.loc&&(A.loc.source=b),A.body=E(y),e.onCacheKey&&(A.cacheKey=e.onCacheKey(b)),R.currentType!==14&&o(y,q.UNEXPECTED_LEXICAL_ANALYSIS,R.lastStartLoc,0,b[R.offset]||""),l(A,y.currentOffset(),y.currentPosition()),A}return{parse:F}}function ft(e){if(e.type===14)return"EOF";const t=(e.value||"").replace(/\r?\n/gu,"\\n");return t.length>10?t.slice(0,9)+"…":t}function kd(e,t={}){const n={ast:e,helpers:new Set};return{context:()=>n,helper:r=>(n.helpers.add(r),r)}}function li(e,t){for(let n=0;n<e.length;n++)to(e[n],t)}function to(e,t){switch(e.type){case 1:li(e.cases,t),t.helper("plural");break;case 2:li(e.items,t);break;case 6:{to(e.key,t),t.helper("linked"),t.helper("type");break}case 5:t.helper("interpolate"),t.helper("list");break;case 4:t.helper("interpolate"),t.helper("named");break}}function wd(e,t={}){const n=kd(e);n.helper("normalize"),e.body&&to(e.body,n);const s=n.context();e.helpers=Array.from(s.helpers)}function Md(e){const t=e.body;return t.type===2?ai(t):t.cases.forEach(n=>ai(n)),e}function ai(e){if(e.items.length===1){const t=e.items[0];(t.type===3||t.type===9)&&(e.static=t.value,delete t.value)}else{const t=[];for(let n=0;n<e.items.length;n++){const s=e.items[n];if(!(s.type===3||s.type===9)||s.value==null)break;t.push(s.value)}if(t.length===e.items.length){e.static=ca(t);for(let n=0;n<e.items.length;n++){const s=e.items[n];(s.type===3||s.type===9)&&delete s.value}}}}const Fd="minifier";function mn(e){switch(e.t=e.type,e.type){case 0:{const t=e;mn(t.body),t.b=t.body,delete t.body;break}case 1:{const t=e,n=t.cases;for(let s=0;s<n.length;s++)mn(n[s]);t.c=n,delete t.cases;break}case 2:{const t=e,n=t.items;for(let s=0;s<n.length;s++)mn(n[s]);t.i=n,delete t.items,t.static&&(t.s=t.static,delete t.static);break}case 3:case 9:case 8:case 7:{const t=e;t.value&&(t.v=t.value,delete t.value);break}case 6:{const t=e;mn(t.key),t.k=t.key,delete t.key,t.modifier&&(mn(t.modifier),t.m=t.modifier,delete t.modifier);break}case 5:{const t=e;t.i=t.index,delete t.index;break}case 4:{const t=e;t.k=t.key,delete t.key;break}default:throw On(q.UNHANDLED_MINIFIER_NODE_TYPE,null,{domain:Fd,args:[e.type]})}delete e.type}const Dd="parser";function xd(e,t){const{filename:n,breakLineCode:s,needIndent:o}=t,r=t.location!==!1,i={filename:n,code:"",column:1,line:1,offset:0,map:void 0,breakLineCode:s,needIndent:o,indentLevel:0};r&&e.loc&&(i.source=e.loc.source);const l=()=>i;function a(C,D){i.code+=C}function c(C,D=!0){const E=D?s:"";a(o?E+"  ".repeat(C):E)}function u(C=!0){const D=++i.indentLevel;C&&c(D)}function f(C=!0){const D=--i.indentLevel;C&&c(D)}function m(){c(i.indentLevel)}return{context:l,push:a,indent:u,deindent:f,newline:m,helper:C=>`_${C}`,needIndent:()=>i.needIndent}}function $d(e,t){const{helper:n}=e;e.push(`${n("linked")}(`),Ln(e,t.key),t.modifier?(e.push(", "),Ln(e,t.modifier),e.push(", _type")):e.push(", undefined, _type"),e.push(")")}function Ud(e,t){const{helper:n,needIndent:s}=e;e.push(`${n("normalize")}([`),e.indent(s());const o=t.items.length;for(let r=0;r<o&&(Ln(e,t.items[r]),r!==o-1);r++)e.push(", ");e.deindent(s()),e.push("])")}function Hd(e,t){const{helper:n,needIndent:s}=e;if(t.cases.length>1){e.push(`${n("plural")}([`),e.indent(s());const o=t.cases.length;for(let r=0;r<o&&(Ln(e,t.cases[r]),r!==o-1);r++)e.push(", ");e.deindent(s()),e.push("])")}}function Vd(e,t){t.body?Ln(e,t.body):e.push("null")}function Ln(e,t){const{helper:n}=e;switch(t.type){case 0:Vd(e,t);break;case 1:Hd(e,t);break;case 2:Ud(e,t);break;case 6:$d(e,t);break;case 8:e.push(JSON.stringify(t.value),t);break;case 7:e.push(JSON.stringify(t.value),t);break;case 5:e.push(`${n("interpolate")}(${n("list")}(${t.index}))`,t);break;case 4:e.push(`${n("interpolate")}(${n("named")}(${JSON.stringify(t.key)}))`,t);break;case 9:e.push(JSON.stringify(t.value),t);break;case 3:e.push(JSON.stringify(t.value),t);break;default:throw On(q.UNHANDLED_CODEGEN_NODE_TYPE,null,{domain:Dd,args:[t.type]})}}const Wd=(e,t={})=>{const n=oi(t.mode)?t.mode:"normal",s=oi(t.filename)?t.filename:"message.intl";t.sourceMap;const o=t.breakLineCode!=null?t.breakLineCode:n==="arrow"?";":`
`,r=t.needIndent?t.needIndent:n!=="arrow",i=e.helpers||[],l=xd(e,{filename:s,breakLineCode:o,needIndent:r});l.push(n==="normal"?"function __msg__ (ctx) {":"(ctx) => {"),l.indent(r),i.length>0&&(l.push(`const { ${ca(i.map(u=>`${u}: _${u}`),", ")} } = ctx`),l.newline()),l.push("return "),Ln(l,e),l.deindent(r),l.push("}"),delete e.helpers;const{code:a,map:c}=l.context();return{ast:e,code:a,map:c?c.toJSON():void 0}};function jd(e,t={}){const n=aa({},t),s=!!n.jit,o=!!n.minify,r=n.optimize==null?!0:n.optimize,l=Rd(n).parse(e);return s?(r&&Md(l),o&&mn(l),{ast:l,code:""}):(wd(l,n),Wd(l,n))}/*!
  * core-base v9.14.4
  * (c) 2025 kazuya kawaguchi
  * Released under the MIT License.
  */function Kd(){typeof __INTLIFY_PROD_DEVTOOLS__!="boolean"&&(Mt().__INTLIFY_PROD_DEVTOOLS__=!1),typeof __INTLIFY_JIT_COMPILATION__!="boolean"&&(Mt().__INTLIFY_JIT_COMPILATION__=!1),typeof __INTLIFY_DROP_MESSAGE_COMPILER__!="boolean"&&(Mt().__INTLIFY_DROP_MESSAGE_COMPILER__=!1)}function St(e){return le(e)&&no(e)===0&&(dt(e,"b")||dt(e,"body"))}const ua=["b","body"];function Bd(e){return qt(e,ua)}const fa=["c","cases"];function Gd(e){return qt(e,fa,[])}const da=["s","static"];function Yd(e){return qt(e,da)}const ha=["i","items"];function zd(e){return qt(e,ha,[])}const ma=["t","type"];function no(e){return qt(e,ma)}const _a=["v","value"];function ls(e,t){const n=qt(e,_a);if(n!=null)return n;throw Xn(t)}const pa=["m","modifier"];function Xd(e){return qt(e,pa)}const ga=["k","key"];function qd(e){const t=qt(e,ga);if(t)return t;throw Xn(6)}function qt(e,t,n){for(let s=0;s<t.length;s++){const o=t[s];if(dt(e,o)&&e[o]!=null)return e[o]}return n}const va=[...ua,...fa,...da,...ha,...ga,...pa,..._a,...ma];function Xn(e){return new Error(`unhandled node type: ${e}`)}const Jt=[];Jt[0]={w:[0],i:[3,0],"[":[4],o:[7]};Jt[1]={w:[1],".":[2],"[":[4],o:[7]};Jt[2]={w:[2],i:[3,0],0:[3,0]};Jt[3]={i:[3,0],0:[3,0],w:[1,1],".":[2,1],"[":[4,1],o:[7,1]};Jt[4]={"'":[5,0],'"':[6,0],"[":[4,2],"]":[1,3],o:8,l:[4,0]};Jt[5]={"'":[4,0],o:8,l:[5,0]};Jt[6]={'"':[4,0],o:8,l:[6,0]};const Jd=/^\s?(?:true|false|-?[\d.]+|'[^']*'|"[^"]*")\s?$/;function Qd(e){return Jd.test(e)}function Zd(e){const t=e.charCodeAt(0),n=e.charCodeAt(e.length-1);return t===n&&(t===34||t===39)?e.slice(1,-1):e}function eh(e){if(e==null)return"o";switch(e.charCodeAt(0)){case 91:case 93:case 46:case 34:case 39:return e;case 95:case 36:case 45:return"i";case 9:case 10:case 13:case 160:case 65279:case 8232:case 8233:return"w"}return"i"}function th(e){const t=e.trim();return e.charAt(0)==="0"&&isNaN(parseInt(e))?!1:Qd(t)?Zd(t):"*"+t}function nh(e){const t=[];let n=-1,s=0,o=0,r,i,l,a,c,u,f;const m=[];m[0]=()=>{i===void 0?i=l:i+=l},m[1]=()=>{i!==void 0&&(t.push(i),i=void 0)},m[2]=()=>{m[0](),o++},m[3]=()=>{if(o>0)o--,s=4,m[0]();else{if(o=0,i===void 0||(i=th(i),i===!1))return!1;m[1]()}};function v(){const O=e[n+1];if(s===5&&O==="'"||s===6&&O==='"')return n++,l="\\"+O,m[0](),!0}for(;s!==null;)if(n++,r=e[n],!(r==="\\"&&v())){if(a=eh(r),f=Jt[s],c=f[a]||f.l||8,c===8||(s=c[0],c[1]!==void 0&&(u=m[c[1]],u&&(l=r,u()===!1))))return;if(s===7)return t}}const ci=new Map;function sh(e,t){return le(e)?e[t]:null}function rh(e,t){if(!le(e))return null;let n=ci.get(t);if(n||(n=nh(t),n&&ci.set(t,n)),!n)return null;const s=n.length;let o=e,r=0;for(;r<s;){const i=n[r];if(va.includes(i)&&St(o))return null;const l=o[i];if(l===void 0||ge(o))return null;o=l,r++}return o}const oh=e=>e,ih=e=>"",lh="text",ah=e=>e.length===0?"":dd(e),ch=fd;function ui(e,t){return e=Math.abs(e),t===2?e?e>1?1:0:1:e?Math.min(e,2):0}function uh(e){const t=Ce(e.pluralIndex)?e.pluralIndex:-1;return e.named&&(Ce(e.named.count)||Ce(e.named.n))?Ce(e.named.count)?e.named.count:Ce(e.named.n)?e.named.n:t:t}function fh(e,t){t.count||(t.count=e),t.n||(t.n=e)}function dh(e={}){const t=e.locale,n=uh(e),s=le(e.pluralRules)&&K(t)&&ge(e.pluralRules[t])?e.pluralRules[t]:ui,o=le(e.pluralRules)&&K(t)&&ge(e.pluralRules[t])?ui:void 0,r=E=>E[s(n,E.length,o)],i=e.list||[],l=E=>i[E],a=e.named||he();Ce(e.pluralIndex)&&fh(n,a);const c=E=>a[E];function u(E){const F=ge(e.messages)?e.messages(E):le(e.messages)?e.messages[E]:!1;return F||(e.parent?e.parent.message(E):ih)}const f=E=>e.modifiers?e.modifiers[E]:oh,m=Q(e.processor)&&ge(e.processor.normalize)?e.processor.normalize:ah,v=Q(e.processor)&&ge(e.processor.interpolate)?e.processor.interpolate:ch,O=Q(e.processor)&&K(e.processor.type)?e.processor.type:lh,D={list:l,named:c,plural:r,linked:(E,...F)=>{const[b,y]=F;let R="text",A="";F.length===1?le(b)?(A=b.modifier||A,R=b.type||R):K(b)&&(A=b||A):F.length===2&&(K(b)&&(A=b||A),K(y)&&(R=y||R));const x=u(E)(D),B=R==="vnode"&&be(x)&&A?x[0]:x;return A?f(A)(B,R):B},message:u,type:O,interpolate:v,normalize:m,values:De(he(),i,a)};return D}let qn=null;function hh(e){qn=e}function mh(e,t,n){qn&&qn.emit("i18n:init",{timestamp:Date.now(),i18n:e,version:t,meta:n})}const _h=ph("function:translate");function ph(e){return t=>qn&&qn.emit(e,t)}const gh=eo.__EXTEND_POINT__,tn=Ks(gh),vh={FALLBACK_TO_TRANSLATE:tn(),CANNOT_FORMAT_NUMBER:tn(),FALLBACK_TO_NUMBER_FORMAT:tn(),CANNOT_FORMAT_DATE:tn(),FALLBACK_TO_DATE_FORMAT:tn(),EXPERIMENTAL_CUSTOM_MESSAGE_COMPILER:tn(),__EXTEND_POINT__:tn()},ba=q.__EXTEND_POINT__,nn=Ks(ba),ht={INVALID_ARGUMENT:ba,INVALID_DATE_ARGUMENT:nn(),INVALID_ISO_DATE_ARGUMENT:nn(),NOT_SUPPORT_NON_STRING_MESSAGE:nn(),NOT_SUPPORT_LOCALE_PROMISE_VALUE:nn(),NOT_SUPPORT_LOCALE_ASYNC_FUNCTION:nn(),NOT_SUPPORT_LOCALE_TYPE:nn(),__EXTEND_POINT__:nn()};function Et(e){return On(e,null,void 0)}function so(e,t){return t.locale!=null?fi(t.locale):fi(e.locale)}let ar;function fi(e){if(K(e))return e;if(ge(e)){if(e.resolvedOnce&&ar!=null)return ar;if(e.constructor.name==="Function"){const t=e();if(ud(t))throw Et(ht.NOT_SUPPORT_LOCALE_PROMISE_VALUE);return ar=t}else throw Et(ht.NOT_SUPPORT_LOCALE_ASYNC_FUNCTION)}else throw Et(ht.NOT_SUPPORT_LOCALE_TYPE)}function bh(e,t,n){return[...new Set([n,...be(t)?t:le(t)?Object.keys(t):K(t)?[t]:[n]])]}function ya(e,t,n){const s=K(n)?n:Cn,o=e;o.__localeChainCache||(o.__localeChainCache=new Map);let r=o.__localeChainCache.get(s);if(!r){r=[];let i=[n];for(;be(i);)i=di(r,i,t);const l=be(t)||!Q(t)?t:t.default?t.default:null;i=K(l)?[l]:l,be(i)&&di(r,i,!1),o.__localeChainCache.set(s,r)}return r}function di(e,t,n){let s=!0;for(let o=0;o<t.length&&ne(s);o++){const r=t[o];K(r)&&(s=yh(e,t[o],n))}return s}function yh(e,t,n){let s;const o=t.split("-");do{const r=o.join("-");s=Eh(e,r,n),o.splice(-1,1)}while(o.length&&s===!0);return s}function Eh(e,t,n){let s=!1;if(!e.includes(t)&&(s=!0,t)){s=t[t.length-1]!=="!";const o=t.replace(/!/g,"");e.push(o),(be(n)||Q(n))&&n[o]&&(s=n[o])}return s}const Th="9.14.4",Bs=-1,Cn="en-US",hi="",mi=e=>`${e.charAt(0).toLocaleUpperCase()}${e.substr(1)}`;function Ih(){return{upper:(e,t)=>t==="text"&&K(e)?e.toUpperCase():t==="vnode"&&le(e)&&"__v_isVNode"in e?e.children.toUpperCase():e,lower:(e,t)=>t==="text"&&K(e)?e.toLowerCase():t==="vnode"&&le(e)&&"__v_isVNode"in e?e.children.toLowerCase():e,capitalize:(e,t)=>t==="text"&&K(e)?mi(e):t==="vnode"&&le(e)&&"__v_isVNode"in e?mi(e.children):e}}let Ea;function _i(e){Ea=e}let Ta;function Sh(e){Ta=e}let Ia;function Lh(e){Ia=e}let Sa=null;const Ch=e=>{Sa=e},Oh=()=>Sa;let La=null;const pi=e=>{La=e},Ph=()=>La;let gi=0;function Nh(e={}){const t=ge(e.onWarn)?e.onWarn:hd,n=K(e.version)?e.version:Th,s=K(e.locale)||ge(e.locale)?e.locale:Cn,o=ge(s)?Cn:s,r=be(e.fallbackLocale)||Q(e.fallbackLocale)||K(e.fallbackLocale)||e.fallbackLocale===!1?e.fallbackLocale:o,i=Q(e.messages)?e.messages:cr(o),l=Q(e.datetimeFormats)?e.datetimeFormats:cr(o),a=Q(e.numberFormats)?e.numberFormats:cr(o),c=De(he(),e.modifiers,Ih()),u=e.pluralRules||he(),f=ge(e.missing)?e.missing:null,m=ne(e.missingWarn)||zt(e.missingWarn)?e.missingWarn:!0,v=ne(e.fallbackWarn)||zt(e.fallbackWarn)?e.fallbackWarn:!0,O=!!e.fallbackFormat,C=!!e.unresolving,D=ge(e.postTranslation)?e.postTranslation:null,E=Q(e.processor)?e.processor:null,F=ne(e.warnHtmlMessage)?e.warnHtmlMessage:!0,b=!!e.escapeParameter,y=ge(e.messageCompiler)?e.messageCompiler:Ea,R=ge(e.messageResolver)?e.messageResolver:Ta||sh,A=ge(e.localeFallbacker)?e.localeFallbacker:Ia||bh,x=le(e.fallbackContext)?e.fallbackContext:void 0,B=e,H=le(B.__datetimeFormatters)?B.__datetimeFormatters:new Map,ae=le(B.__numberFormatters)?B.__numberFormatters:new Map,Te=le(B.__meta)?B.__meta:{};gi++;const ee={version:n,cid:gi,locale:s,fallbackLocale:r,messages:i,modifiers:c,pluralRules:u,missing:f,missingWarn:m,fallbackWarn:v,fallbackFormat:O,unresolving:C,postTranslation:D,processor:E,warnHtmlMessage:F,escapeParameter:b,messageCompiler:y,messageResolver:R,localeFallbacker:A,fallbackContext:x,onWarn:t,__meta:Te};return ee.datetimeFormats=l,ee.numberFormats=a,ee.__datetimeFormatters=H,ee.__numberFormatters=ae,__INTLIFY_PROD_DEVTOOLS__&&mh(ee,n,Te),ee}const cr=e=>({[e]:he()});function ro(e,t,n,s,o){const{missing:r,onWarn:i}=e;if(r!==null){const l=r(e,n,t,o);return K(l)?l:t}else return t}function Rn(e,t,n){const s=e;s.__localeChainCache=new Map,e.localeFallbacker(e,n,t)}function Ah(e,t){return e===t?!1:e.split("-")[0]===t.split("-")[0]}function Rh(e,t){const n=t.indexOf(e);if(n===-1)return!1;for(let s=n+1;s<t.length;s++)if(Ah(e,t[s]))return!0;return!1}function ur(e){return n=>kh(n,e)}function kh(e,t){const n=Bd(t);if(n==null)throw Xn(0);if(no(n)===1){const r=Gd(n);return e.plural(r.reduce((i,l)=>[...i,vi(e,l)],[]))}else return vi(e,n)}function vi(e,t){const n=Yd(t);if(n!=null)return e.type==="text"?n:e.normalize([n]);{const s=zd(t).reduce((o,r)=>[...o,Lr(e,r)],[]);return e.normalize(s)}}function Lr(e,t){const n=no(t);switch(n){case 3:return ls(t,n);case 9:return ls(t,n);case 4:{const s=t;if(dt(s,"k")&&s.k)return e.interpolate(e.named(s.k));if(dt(s,"key")&&s.key)return e.interpolate(e.named(s.key));throw Xn(n)}case 5:{const s=t;if(dt(s,"i")&&Ce(s.i))return e.interpolate(e.list(s.i));if(dt(s,"index")&&Ce(s.index))return e.interpolate(e.list(s.index));throw Xn(n)}case 6:{const s=t,o=Xd(s),r=qd(s);return e.linked(Lr(e,r),o?Lr(e,o):void 0,e.type)}case 7:return ls(t,n);case 8:return ls(t,n);default:throw new Error(`unhandled node on format message part: ${n}`)}}const Ca=e=>e;let _n=he();function Oa(e,t={}){let n=!1;const s=t.onError||yd;return t.onError=o=>{n=!0,s(o)},{...jd(e,t),detectError:n}}const wh=(e,t)=>{if(!K(e))throw Et(ht.NOT_SUPPORT_NON_STRING_MESSAGE);{ne(t.warnHtmlMessage)&&t.warnHtmlMessage;const s=(t.onCacheKey||Ca)(e),o=_n[s];if(o)return o;const{code:r,detectError:i}=Oa(e,t),l=new Function(`return ${r}`)();return i?l:_n[s]=l}};function Mh(e,t){if(__INTLIFY_JIT_COMPILATION__&&!__INTLIFY_DROP_MESSAGE_COMPILER__&&K(e)){ne(t.warnHtmlMessage)&&t.warnHtmlMessage;const s=(t.onCacheKey||Ca)(e),o=_n[s];if(o)return o;const{ast:r,detectError:i}=Oa(e,{...t,location:!1,jit:!0}),l=ur(r);return i?l:_n[s]=l}else{const n=e.cacheKey;if(n){const s=_n[n];return s||(_n[n]=ur(e))}else return ur(e)}}const bi=()=>"",at=e=>ge(e);function yi(e,...t){const{fallbackFormat:n,postTranslation:s,unresolving:o,messageCompiler:r,fallbackLocale:i,messages:l}=e,[a,c]=Cr(...t),u=ne(c.missingWarn)?c.missingWarn:e.missingWarn,f=ne(c.fallbackWarn)?c.fallbackWarn:e.fallbackWarn,m=ne(c.escapeParameter)?c.escapeParameter:e.escapeParameter,v=!!c.resolvedMessage,O=K(c.default)||ne(c.default)?ne(c.default)?r?a:()=>a:c.default:n?r?a:()=>a:"",C=n||O!=="",D=so(e,c);m&&Fh(c);let[E,F,b]=v?[a,D,l[D]||he()]:Pa(e,a,D,i,f,u),y=E,R=a;if(!v&&!(K(y)||St(y)||at(y))&&C&&(y=O,R=y),!v&&(!(K(y)||St(y)||at(y))||!K(F)))return o?Bs:a;let A=!1;const x=()=>{A=!0},B=at(y)?y:Na(e,a,F,y,R,x);if(A)return y;const H=$h(e,F,b,c),ae=dh(H),Te=Dh(e,B,ae),ee=s?s(Te,a):Te;if(__INTLIFY_PROD_DEVTOOLS__){const me={timestamp:Date.now(),key:K(a)?a:at(y)?y.key:"",locale:F||(at(y)?y.locale:""),format:K(y)?y:at(y)?y.source:"",message:ee};me.meta=De({},e.__meta,Oh()||{}),_h(me)}return ee}function Fh(e){be(e.list)?e.list=e.list.map(t=>K(t)?ri(t):t):le(e.named)&&Object.keys(e.named).forEach(t=>{K(e.named[t])&&(e.named[t]=ri(e.named[t]))})}function Pa(e,t,n,s,o,r){const{messages:i,onWarn:l,messageResolver:a,localeFallbacker:c}=e,u=c(e,s,n);let f=he(),m,v=null;const O="translate";for(let C=0;C<u.length&&(m=u[C],f=i[m]||he(),(v=a(f,t))===null&&(v=f[t]),!(K(v)||St(v)||at(v)));C++)if(!Rh(m,u)){const D=ro(e,t,m,r,O);D!==t&&(v=D)}return[v,m,f]}function Na(e,t,n,s,o,r){const{messageCompiler:i,warnHtmlMessage:l}=e;if(at(s)){const c=s;return c.locale=c.locale||n,c.key=c.key||t,c}if(i==null){const c=()=>s;return c.locale=n,c.key=t,c}const a=i(s,xh(e,n,o,s,l,r));return a.locale=n,a.key=t,a.source=s,a}function Dh(e,t,n){return t(n)}function Cr(...e){const[t,n,s]=e,o=he();if(!K(t)&&!Ce(t)&&!at(t)&&!St(t))throw Et(ht.INVALID_ARGUMENT);const r=Ce(t)?String(t):(at(t),t);return Ce(n)?o.plural=n:K(n)?o.default=n:Q(n)&&!js(n)?o.named=n:be(n)&&(o.list=n),Ce(s)?o.plural=s:K(s)?o.default=s:Q(s)&&De(o,s),[r,o]}function xh(e,t,n,s,o,r){return{locale:t,key:n,warnHtmlMessage:o,onError:i=>{throw r&&r(i),i},onCacheKey:i=>od(t,n,i)}}function $h(e,t,n,s){const{modifiers:o,pluralRules:r,messageResolver:i,fallbackLocale:l,fallbackWarn:a,missingWarn:c,fallbackContext:u}=e,m={locale:t,modifiers:o,pluralRules:r,messages:v=>{let O=i(n,v);if(O==null&&u){const[,,C]=Pa(u,v,t,l,a,c);O=i(C,v)}if(K(O)||St(O)){let C=!1;const E=Na(e,v,t,O,v,()=>{C=!0});return C?bi:E}else return at(O)?O:bi}};return e.processor&&(m.processor=e.processor),s.list&&(m.list=s.list),s.named&&(m.named=s.named),Ce(s.plural)&&(m.pluralIndex=s.plural),m}function Ei(e,...t){const{datetimeFormats:n,unresolving:s,fallbackLocale:o,onWarn:r,localeFallbacker:i}=e,{__datetimeFormatters:l}=e,[a,c,u,f]=Or(...t),m=ne(u.missingWarn)?u.missingWarn:e.missingWarn;ne(u.fallbackWarn)?u.fallbackWarn:e.fallbackWarn;const v=!!u.part,O=so(e,u),C=i(e,o,O);if(!K(a)||a==="")return new Intl.DateTimeFormat(O,f).format(c);let D={},E,F=null;const b="datetime format";for(let A=0;A<C.length&&(E=C[A],D=n[E]||{},F=D[a],!Q(F));A++)ro(e,a,E,m,b);if(!Q(F)||!K(E))return s?Bs:a;let y=`${E}__${a}`;js(f)||(y=`${y}__${JSON.stringify(f)}`);let R=l.get(y);return R||(R=new Intl.DateTimeFormat(E,De({},F,f)),l.set(y,R)),v?R.formatToParts(c):R.format(c)}const Aa=["localeMatcher","weekday","era","year","month","day","hour","minute","second","timeZoneName","formatMatcher","hour12","timeZone","dateStyle","timeStyle","calendar","dayPeriod","numberingSystem","hourCycle","fractionalSecondDigits"];function Or(...e){const[t,n,s,o]=e,r=he();let i=he(),l;if(K(t)){const a=t.match(/(\d{4}-\d{2}-\d{2})(T|\s)?(.*)/);if(!a)throw Et(ht.INVALID_ISO_DATE_ARGUMENT);const c=a[3]?a[3].trim().startsWith("T")?`${a[1].trim()}${a[3].trim()}`:`${a[1].trim()}T${a[3].trim()}`:a[1].trim();l=new Date(c);try{l.toISOString()}catch{throw Et(ht.INVALID_ISO_DATE_ARGUMENT)}}else if(ld(t)){if(isNaN(t.getTime()))throw Et(ht.INVALID_DATE_ARGUMENT);l=t}else if(Ce(t))l=t;else throw Et(ht.INVALID_ARGUMENT);return K(n)?r.key=n:Q(n)&&Object.keys(n).forEach(a=>{Aa.includes(a)?i[a]=n[a]:r[a]=n[a]}),K(s)?r.locale=s:Q(s)&&(i=s),Q(o)&&(i=o),[r.key||"",l,r,i]}function Ti(e,t,n){const s=e;for(const o in n){const r=`${t}__${o}`;s.__datetimeFormatters.has(r)&&s.__datetimeFormatters.delete(r)}}function Ii(e,...t){const{numberFormats:n,unresolving:s,fallbackLocale:o,onWarn:r,localeFallbacker:i}=e,{__numberFormatters:l}=e,[a,c,u,f]=Pr(...t),m=ne(u.missingWarn)?u.missingWarn:e.missingWarn;ne(u.fallbackWarn)?u.fallbackWarn:e.fallbackWarn;const v=!!u.part,O=so(e,u),C=i(e,o,O);if(!K(a)||a==="")return new Intl.NumberFormat(O,f).format(c);let D={},E,F=null;const b="number format";for(let A=0;A<C.length&&(E=C[A],D=n[E]||{},F=D[a],!Q(F));A++)ro(e,a,E,m,b);if(!Q(F)||!K(E))return s?Bs:a;let y=`${E}__${a}`;js(f)||(y=`${y}__${JSON.stringify(f)}`);let R=l.get(y);return R||(R=new Intl.NumberFormat(E,De({},F,f)),l.set(y,R)),v?R.formatToParts(c):R.format(c)}const Ra=["localeMatcher","style","currency","currencyDisplay","currencySign","useGrouping","minimumIntegerDigits","minimumFractionDigits","maximumFractionDigits","minimumSignificantDigits","maximumSignificantDigits","compactDisplay","notation","signDisplay","unit","unitDisplay","roundingMode","roundingPriority","roundingIncrement","trailingZeroDisplay"];function Pr(...e){const[t,n,s,o]=e,r=he();let i=he();if(!Ce(t))throw Et(ht.INVALID_ARGUMENT);const l=t;return K(n)?r.key=n:Q(n)&&Object.keys(n).forEach(a=>{Ra.includes(a)?i[a]=n[a]:r[a]=n[a]}),K(s)?r.locale=s:Q(s)&&(i=s),Q(o)&&(i=o),[r.key||"",l,r,i]}function Si(e,t,n){const s=e;for(const o in n){const r=`${t}__${o}`;s.__numberFormatters.has(r)&&s.__numberFormatters.delete(r)}}Kd();/*!
  * vue-i18n v9.14.4
  * (c) 2025 kazuya kawaguchi
  * Released under the MIT License.
  */const Uh="9.14.4";function Hh(){typeof __VUE_I18N_FULL_INSTALL__!="boolean"&&(Mt().__VUE_I18N_FULL_INSTALL__=!0),typeof __VUE_I18N_LEGACY_API__!="boolean"&&(Mt().__VUE_I18N_LEGACY_API__=!0),typeof __INTLIFY_JIT_COMPILATION__!="boolean"&&(Mt().__INTLIFY_JIT_COMPILATION__=!1),typeof __INTLIFY_DROP_MESSAGE_COMPILER__!="boolean"&&(Mt().__INTLIFY_DROP_MESSAGE_COMPILER__=!1),typeof __INTLIFY_PROD_DEVTOOLS__!="boolean"&&(Mt().__INTLIFY_PROD_DEVTOOLS__=!1)}const Vh=vh.__EXTEND_POINT__,At=Ks(Vh);At(),At(),At(),At(),At(),At(),At(),At(),At();const ka=ht.__EXTEND_POINT__,Je=Ks(ka),Ne={UNEXPECTED_RETURN_TYPE:ka,INVALID_ARGUMENT:Je(),MUST_BE_CALL_SETUP_TOP:Je(),NOT_INSTALLED:Je(),NOT_AVAILABLE_IN_LEGACY_MODE:Je(),REQUIRED_VALUE:Je(),INVALID_VALUE:Je(),CANNOT_SETUP_VUE_DEVTOOLS_PLUGIN:Je(),NOT_INSTALLED_WITH_PROVIDE:Je(),UNEXPECTED_ERROR:Je(),NOT_COMPATIBLE_LEGACY_VUE_I18N:Je(),BRIDGE_SUPPORT_VUE_2_ONLY:Je(),MUST_DEFINE_I18N_OPTION_IN_ALLOW_COMPOSITION:Je(),NOT_AVAILABLE_COMPOSITION_IN_LEGACY:Je(),__EXTEND_POINT__:Je()};function Re(e,...t){return On(e,null,void 0)}const Nr=Xt("__translateVNode"),Ar=Xt("__datetimeParts"),Rr=Xt("__numberParts"),wa=Xt("__setPluralRules"),Ma=Xt("__injectWithOption"),kr=Xt("__dispose");function Jn(e){if(!le(e)||St(e))return e;for(const t in e)if(dt(e,t))if(!t.includes("."))le(e[t])&&Jn(e[t]);else{const n=t.split("."),s=n.length-1;let o=e,r=!1;for(let i=0;i<s;i++){if(n[i]==="__proto__")throw new Error(`unsafe key: ${n[i]}`);if(n[i]in o||(o[n[i]]=he()),!le(o[n[i]])){r=!0;break}o=o[n[i]]}if(r||(St(o)?va.includes(n[s])||delete e[t]:(o[n[s]]=e[t],delete e[t])),!St(o)){const i=o[n[s]];le(i)&&Jn(i)}}return e}function Gs(e,t){const{messages:n,__i18n:s,messageResolver:o,flatJson:r}=t,i=Q(n)?n:be(s)?he():{[e]:he()};if(be(s)&&s.forEach(l=>{if("locale"in l&&"resource"in l){const{locale:a,resource:c}=l;a?(i[a]=i[a]||he(),_s(c,i[a])):_s(c,i)}else K(l)&&_s(JSON.parse(l),i)}),o==null&&r)for(const l in i)dt(i,l)&&Jn(i[l]);return i}function Fa(e){return e.type}function Da(e,t,n){let s=le(t.messages)?t.messages:he();"__i18nGlobal"in n&&(s=Gs(e.locale.value,{messages:s,__i18n:n.__i18nGlobal}));const o=Object.keys(s);o.length&&o.forEach(r=>{e.mergeLocaleMessage(r,s[r])});{if(le(t.datetimeFormats)){const r=Object.keys(t.datetimeFormats);r.length&&r.forEach(i=>{e.mergeDateTimeFormat(i,t.datetimeFormats[i])})}if(le(t.numberFormats)){const r=Object.keys(t.numberFormats);r.length&&r.forEach(i=>{e.mergeNumberFormat(i,t.numberFormats[i])})}}}function Li(e){return te(ts,null,e,0)}const Ci="__INTLIFY_META__",Oi=()=>[],Wh=()=>!1;let Pi=0;function Ni(e){return(t,n,s,o)=>e(n,s,Bn()||void 0,o)}const jh=()=>{const e=Bn();let t=null;return e&&(t=Fa(e)[Ci])?{[Ci]:t}:null};function oo(e={},t){const{__root:n,__injectWithOption:s}=e,o=n===void 0,r=e.flatJson,i=Ps?Ve:Br,l=!!e.translateExistCompatible;let a=ne(e.inheritLocale)?e.inheritLocale:!0;const c=i(n&&a?n.locale.value:K(e.locale)?e.locale:Cn),u=i(n&&a?n.fallbackLocale.value:K(e.fallbackLocale)||be(e.fallbackLocale)||Q(e.fallbackLocale)||e.fallbackLocale===!1?e.fallbackLocale:c.value),f=i(Gs(c.value,e)),m=i(Q(e.datetimeFormats)?e.datetimeFormats:{[c.value]:{}}),v=i(Q(e.numberFormats)?e.numberFormats:{[c.value]:{}});let O=n?n.missingWarn:ne(e.missingWarn)||zt(e.missingWarn)?e.missingWarn:!0,C=n?n.fallbackWarn:ne(e.fallbackWarn)||zt(e.fallbackWarn)?e.fallbackWarn:!0,D=n?n.fallbackRoot:ne(e.fallbackRoot)?e.fallbackRoot:!0,E=!!e.fallbackFormat,F=ge(e.missing)?e.missing:null,b=ge(e.missing)?Ni(e.missing):null,y=ge(e.postTranslation)?e.postTranslation:null,R=n?n.warnHtmlMessage:ne(e.warnHtmlMessage)?e.warnHtmlMessage:!0,A=!!e.escapeParameter;const x=n?n.modifiers:Q(e.modifiers)?e.modifiers:{};let B=e.pluralRules||n&&n.pluralRules,H;H=(()=>{o&&pi(null);const T={version:Uh,locale:c.value,fallbackLocale:u.value,messages:f.value,modifiers:x,pluralRules:B,missing:b===null?void 0:b,missingWarn:O,fallbackWarn:C,fallbackFormat:E,unresolving:!0,postTranslation:y===null?void 0:y,warnHtmlMessage:R,escapeParameter:A,messageResolver:e.messageResolver,messageCompiler:e.messageCompiler,__meta:{framework:"vue"}};T.datetimeFormats=m.value,T.numberFormats=v.value,T.__datetimeFormatters=Q(H)?H.__datetimeFormatters:void 0,T.__numberFormatters=Q(H)?H.__numberFormatters:void 0;const N=Nh(T);return o&&pi(N),N})(),Rn(H,c.value,u.value);function Te(){return[c.value,u.value,f.value,m.value,v.value]}const ee=Ee({get:()=>c.value,set:T=>{c.value=T,H.locale=c.value}}),me=Ee({get:()=>u.value,set:T=>{u.value=T,H.fallbackLocale=u.value,Rn(H,c.value,T)}}),ut=Ee(()=>f.value),ot=Ee(()=>m.value),de=Ee(()=>v.value);function oe(){return ge(y)?y:null}function se(T){y=T,H.postTranslation=T}function Ke(){return F}function qe(T){T!==null&&(b=Ni(T)),F=T,H.missing=b}const Ie=(T,N,z,re,ye,Be)=>{Te();let ke;try{__INTLIFY_PROD_DEVTOOLS__,o||(H.fallbackContext=n?Ph():void 0),ke=T(H)}finally{__INTLIFY_PROD_DEVTOOLS__,o||(H.fallbackContext=void 0)}if(z!=="translate exists"&&Ce(ke)&&ke===Bs||z==="translate exists"&&!ke){const[Qt,Xs]=N();return n&&D?re(n):ye(Qt)}else{if(Be(ke))return ke;throw Re(Ne.UNEXPECTED_RETURN_TYPE)}};function Se(...T){return Ie(N=>Reflect.apply(yi,null,[N,...T]),()=>Cr(...T),"translate",N=>Reflect.apply(N.t,N,[...T]),N=>N,N=>K(N))}function it(...T){const[N,z,re]=T;if(re&&!le(re))throw Re(Ne.INVALID_ARGUMENT);return Se(N,z,De({resolvedMessage:!0},re||{}))}function et(...T){return Ie(N=>Reflect.apply(Ei,null,[N,...T]),()=>Or(...T),"datetime format",N=>Reflect.apply(N.d,N,[...T]),()=>hi,N=>K(N))}function pt(...T){return Ie(N=>Reflect.apply(Ii,null,[N,...T]),()=>Pr(...T),"number format",N=>Reflect.apply(N.n,N,[...T]),()=>hi,N=>K(N))}function Ae(T){return T.map(N=>K(N)||Ce(N)||ne(N)?Li(String(N)):N)}const G={normalize:Ae,interpolate:T=>T,type:"vnode"};function j(...T){return Ie(N=>{let z;const re=N;try{re.processor=G,z=Reflect.apply(yi,null,[re,...T])}finally{re.processor=null}return z},()=>Cr(...T),"translate",N=>N[Nr](...T),N=>[Li(N)],N=>be(N))}function Y(...T){return Ie(N=>Reflect.apply(Ii,null,[N,...T]),()=>Pr(...T),"number format",N=>N[Rr](...T),Oi,N=>K(N)||be(N))}function ie(...T){return Ie(N=>Reflect.apply(Ei,null,[N,...T]),()=>Or(...T),"datetime format",N=>N[Ar](...T),Oi,N=>K(N)||be(N))}function p(T){B=T,H.pluralRules=B}function g(T,N){return Ie(()=>{if(!T)return!1;const z=K(N)?N:c.value,re=M(z),ye=H.messageResolver(re,T);return l?ye!=null:St(ye)||at(ye)||K(ye)},()=>[T],"translate exists",z=>Reflect.apply(z.te,z,[T,N]),Wh,z=>ne(z))}function _(T){let N=null;const z=ya(H,u.value,c.value);for(let re=0;re<z.length;re++){const ye=f.value[z[re]]||{},Be=H.messageResolver(ye,T);if(Be!=null){N=Be;break}}return N}function S(T){const N=_(T);return N??(n?n.tm(T)||{}:{})}function M(T){return f.value[T]||{}}function k(T,N){if(r){const z={[T]:N};for(const re in z)dt(z,re)&&Jn(z[re]);N=z[T]}f.value[T]=N,H.messages=f.value}function V(T,N){f.value[T]=f.value[T]||{};const z={[T]:N};if(r)for(const re in z)dt(z,re)&&Jn(z[re]);N=z[T],_s(N,f.value[T]),H.messages=f.value}function U(T){return m.value[T]||{}}function d(T,N){m.value[T]=N,H.datetimeFormats=m.value,Ti(H,T,N)}function h(T,N){m.value[T]=De(m.value[T]||{},N),H.datetimeFormats=m.value,Ti(H,T,N)}function I(T){return v.value[T]||{}}function P(T,N){v.value[T]=N,H.numberFormats=v.value,Si(H,T,N)}function W(T,N){v.value[T]=De(v.value[T]||{},N),H.numberFormats=v.value,Si(H,T,N)}Pi++,n&&Ps&&(ln(n.locale,T=>{a&&(c.value=T,H.locale=T,Rn(H,c.value,u.value))}),ln(n.fallbackLocale,T=>{a&&(u.value=T,H.fallbackLocale=T,Rn(H,c.value,u.value))}));const $={id:Pi,locale:ee,fallbackLocale:me,get inheritLocale(){return a},set inheritLocale(T){a=T,T&&n&&(c.value=n.locale.value,u.value=n.fallbackLocale.value,Rn(H,c.value,u.value))},get availableLocales(){return Object.keys(f.value).sort()},messages:ut,get modifiers(){return x},get pluralRules(){return B||{}},get isGlobal(){return o},get missingWarn(){return O},set missingWarn(T){O=T,H.missingWarn=O},get fallbackWarn(){return C},set fallbackWarn(T){C=T,H.fallbackWarn=C},get fallbackRoot(){return D},set fallbackRoot(T){D=T},get fallbackFormat(){return E},set fallbackFormat(T){E=T,H.fallbackFormat=E},get warnHtmlMessage(){return R},set warnHtmlMessage(T){R=T,H.warnHtmlMessage=T},get escapeParameter(){return A},set escapeParameter(T){A=T,H.escapeParameter=T},t:Se,getLocaleMessage:M,setLocaleMessage:k,mergeLocaleMessage:V,getPostTranslationHandler:oe,setPostTranslationHandler:se,getMissingHandler:Ke,setMissingHandler:qe,[wa]:p};return $.datetimeFormats=ot,$.numberFormats=de,$.rt=it,$.te=g,$.tm=S,$.d=et,$.n=pt,$.getDateTimeFormat=U,$.setDateTimeFormat=d,$.mergeDateTimeFormat=h,$.getNumberFormat=I,$.setNumberFormat=P,$.mergeNumberFormat=W,$[Ma]=s,$[Nr]=j,$[Ar]=ie,$[Rr]=Y,$}function Kh(e){const t=K(e.locale)?e.locale:Cn,n=K(e.fallbackLocale)||be(e.fallbackLocale)||Q(e.fallbackLocale)||e.fallbackLocale===!1?e.fallbackLocale:t,s=ge(e.missing)?e.missing:void 0,o=ne(e.silentTranslationWarn)||zt(e.silentTranslationWarn)?!e.silentTranslationWarn:!0,r=ne(e.silentFallbackWarn)||zt(e.silentFallbackWarn)?!e.silentFallbackWarn:!0,i=ne(e.fallbackRoot)?e.fallbackRoot:!0,l=!!e.formatFallbackMessages,a=Q(e.modifiers)?e.modifiers:{},c=e.pluralizationRules,u=ge(e.postTranslation)?e.postTranslation:void 0,f=K(e.warnHtmlInMessage)?e.warnHtmlInMessage!=="off":!0,m=!!e.escapeParameterHtml,v=ne(e.sync)?e.sync:!0;let O=e.messages;if(Q(e.sharedMessages)){const A=e.sharedMessages;O=Object.keys(A).reduce((B,H)=>{const ae=B[H]||(B[H]={});return De(ae,A[H]),B},O||{})}const{__i18n:C,__root:D,__injectWithOption:E}=e,F=e.datetimeFormats,b=e.numberFormats,y=e.flatJson,R=e.translateExistCompatible;return{locale:t,fallbackLocale:n,messages:O,flatJson:y,datetimeFormats:F,numberFormats:b,missing:s,missingWarn:o,fallbackWarn:r,fallbackRoot:i,fallbackFormat:l,modifiers:a,pluralRules:c,postTranslation:u,warnHtmlMessage:f,escapeParameter:m,messageResolver:e.messageResolver,inheritLocale:v,translateExistCompatible:R,__i18n:C,__root:D,__injectWithOption:E}}function wr(e={},t){{const n=oo(Kh(e)),{__extender:s}=e,o={id:n.id,get locale(){return n.locale.value},set locale(r){n.locale.value=r},get fallbackLocale(){return n.fallbackLocale.value},set fallbackLocale(r){n.fallbackLocale.value=r},get messages(){return n.messages.value},get datetimeFormats(){return n.datetimeFormats.value},get numberFormats(){return n.numberFormats.value},get availableLocales(){return n.availableLocales},get formatter(){return{interpolate(){return[]}}},set formatter(r){},get missing(){return n.getMissingHandler()},set missing(r){n.setMissingHandler(r)},get silentTranslationWarn(){return ne(n.missingWarn)?!n.missingWarn:n.missingWarn},set silentTranslationWarn(r){n.missingWarn=ne(r)?!r:r},get silentFallbackWarn(){return ne(n.fallbackWarn)?!n.fallbackWarn:n.fallbackWarn},set silentFallbackWarn(r){n.fallbackWarn=ne(r)?!r:r},get modifiers(){return n.modifiers},get formatFallbackMessages(){return n.fallbackFormat},set formatFallbackMessages(r){n.fallbackFormat=r},get postTranslation(){return n.getPostTranslationHandler()},set postTranslation(r){n.setPostTranslationHandler(r)},get sync(){return n.inheritLocale},set sync(r){n.inheritLocale=r},get warnHtmlInMessage(){return n.warnHtmlMessage?"warn":"off"},set warnHtmlInMessage(r){n.warnHtmlMessage=r!=="off"},get escapeParameterHtml(){return n.escapeParameter},set escapeParameterHtml(r){n.escapeParameter=r},get preserveDirectiveContent(){return!0},set preserveDirectiveContent(r){},get pluralizationRules(){return n.pluralRules||{}},__composer:n,t(...r){const[i,l,a]=r,c={};let u=null,f=null;if(!K(i))throw Re(Ne.INVALID_ARGUMENT);const m=i;return K(l)?c.locale=l:be(l)?u=l:Q(l)&&(f=l),be(a)?u=a:Q(a)&&(f=a),Reflect.apply(n.t,n,[m,u||f||{},c])},rt(...r){return Reflect.apply(n.rt,n,[...r])},tc(...r){const[i,l,a]=r,c={plural:1};let u=null,f=null;if(!K(i))throw Re(Ne.INVALID_ARGUMENT);const m=i;return K(l)?c.locale=l:Ce(l)?c.plural=l:be(l)?u=l:Q(l)&&(f=l),K(a)?c.locale=a:be(a)?u=a:Q(a)&&(f=a),Reflect.apply(n.t,n,[m,u||f||{},c])},te(r,i){return n.te(r,i)},tm(r){return n.tm(r)},getLocaleMessage(r){return n.getLocaleMessage(r)},setLocaleMessage(r,i){n.setLocaleMessage(r,i)},mergeLocaleMessage(r,i){n.mergeLocaleMessage(r,i)},d(...r){return Reflect.apply(n.d,n,[...r])},getDateTimeFormat(r){return n.getDateTimeFormat(r)},setDateTimeFormat(r,i){n.setDateTimeFormat(r,i)},mergeDateTimeFormat(r,i){n.mergeDateTimeFormat(r,i)},n(...r){return Reflect.apply(n.n,n,[...r])},getNumberFormat(r){return n.getNumberFormat(r)},setNumberFormat(r,i){n.setNumberFormat(r,i)},mergeNumberFormat(r,i){n.mergeNumberFormat(r,i)},getChoiceIndex(r,i){return-1}};return o.__extender=s,o}}const io={tag:{type:[String,Object]},locale:{type:String},scope:{type:String,validator:e=>e==="parent"||e==="global",default:"parent"},i18n:{type:Object}};function Bh({slots:e},t){return t.length===1&&t[0]==="default"?(e.default?e.default():[]).reduce((s,o)=>[...s,...o.type===Xe?o.children:[o]],[]):t.reduce((n,s)=>{const o=e[s];return o&&(n[s]=o()),n},he())}function xa(e){return Xe}const Gh=Ze({name:"i18n-t",props:De({keypath:{type:String,required:!0},plural:{type:[Number,String],validator:e=>Ce(e)||!isNaN(e)}},io),setup(e,t){const{slots:n,attrs:s}=t,o=e.i18n||Ys({useScope:e.scope,__useComponent:!0});return()=>{const r=Object.keys(n).filter(f=>f!=="_"),i=he();e.locale&&(i.locale=e.locale),e.plural!==void 0&&(i.plural=K(e.plural)?+e.plural:e.plural);const l=Bh(t,r),a=o[Nr](e.keypath,l,i),c=De(he(),s),u=K(e.tag)||le(e.tag)?e.tag:xa();return Vs(u,c,a)}}}),Ai=Gh;function Yh(e){return be(e)&&!K(e[0])}function $a(e,t,n,s){const{slots:o,attrs:r}=t;return()=>{const i={part:!0};let l=he();e.locale&&(i.locale=e.locale),K(e.format)?i.key=e.format:le(e.format)&&(K(e.format.key)&&(i.key=e.format.key),l=Object.keys(e.format).reduce((m,v)=>n.includes(v)?De(he(),m,{[v]:e.format[v]}):m,he()));const a=s(e.value,i,l);let c=[i.key];be(a)?c=a.map((m,v)=>{const O=o[m.type],C=O?O({[m.type]:m.value,index:v,parts:a}):[m.value];return Yh(C)&&(C[0].key=`${m.type}-${v}`),C}):K(a)&&(c=[a]);const u=De(he(),r),f=K(e.tag)||le(e.tag)?e.tag:xa();return Vs(f,u,c)}}const zh=Ze({name:"i18n-n",props:De({value:{type:Number,required:!0},format:{type:[String,Object]}},io),setup(e,t){const n=e.i18n||Ys({useScope:e.scope,__useComponent:!0});return $a(e,t,Ra,(...s)=>n[Rr](...s))}}),Ri=zh,Xh=Ze({name:"i18n-d",props:De({value:{type:[Number,Date],required:!0},format:{type:[String,Object]}},io),setup(e,t){const n=e.i18n||Ys({useScope:e.scope,__useComponent:!0});return $a(e,t,Aa,(...s)=>n[Ar](...s))}}),ki=Xh;function qh(e,t){const n=e;if(e.mode==="composition")return n.__getInstance(t)||e.global;{const s=n.__getInstance(t);return s!=null?s.__composer:e.global.__composer}}function Jh(e){const t=i=>{const{instance:l,modifiers:a,value:c}=i;if(!l||!l.$)throw Re(Ne.UNEXPECTED_ERROR);const u=qh(e,l.$),f=wi(c);return[Reflect.apply(u.t,u,[...Mi(f)]),u]};return{created:(i,l)=>{const[a,c]=t(l);Ps&&e.global===c&&(i.__i18nWatcher=ln(c.locale,()=>{l.instance&&l.instance.$forceUpdate()})),i.__composer=c,i.textContent=a},unmounted:i=>{Ps&&i.__i18nWatcher&&(i.__i18nWatcher(),i.__i18nWatcher=void 0,delete i.__i18nWatcher),i.__composer&&(i.__composer=void 0,delete i.__composer)},beforeUpdate:(i,{value:l})=>{if(i.__composer){const a=i.__composer,c=wi(l);i.textContent=Reflect.apply(a.t,a,[...Mi(c)])}},getSSRProps:i=>{const[l]=t(i);return{textContent:l}}}}function wi(e){if(K(e))return{path:e};if(Q(e)){if(!("path"in e))throw Re(Ne.REQUIRED_VALUE,"path");return e}else throw Re(Ne.INVALID_VALUE)}function Mi(e){const{path:t,locale:n,args:s,choice:o,plural:r}=e,i={},l=s||{};return K(n)&&(i.locale=n),Ce(o)&&(i.plural=o),Ce(r)&&(i.plural=r),[t,l,i]}function Qh(e,t,...n){const s=Q(n[0])?n[0]:{},o=!!s.useI18nComponentName;(ne(s.globalInstall)?s.globalInstall:!0)&&([o?"i18n":Ai.name,"I18nT"].forEach(i=>e.component(i,Ai)),[Ri.name,"I18nN"].forEach(i=>e.component(i,Ri)),[ki.name,"I18nD"].forEach(i=>e.component(i,ki))),e.directive("t",Jh(t))}function Zh(e,t,n){return{beforeCreate(){const s=Bn();if(!s)throw Re(Ne.UNEXPECTED_ERROR);const o=this.$options;if(o.i18n){const r=o.i18n;if(o.__i18n&&(r.__i18n=o.__i18n),r.__root=t,this===this.$root)this.$i18n=Fi(e,r);else{r.__injectWithOption=!0,r.__extender=n.__vueI18nExtend,this.$i18n=wr(r);const i=this.$i18n;i.__extender&&(i.__disposer=i.__extender(this.$i18n))}}else if(o.__i18n)if(this===this.$root)this.$i18n=Fi(e,o);else{this.$i18n=wr({__i18n:o.__i18n,__injectWithOption:!0,__extender:n.__vueI18nExtend,__root:t});const r=this.$i18n;r.__extender&&(r.__disposer=r.__extender(this.$i18n))}else this.$i18n=e;o.__i18nGlobal&&Da(t,o,o),this.$t=(...r)=>this.$i18n.t(...r),this.$rt=(...r)=>this.$i18n.rt(...r),this.$tc=(...r)=>this.$i18n.tc(...r),this.$te=(r,i)=>this.$i18n.te(r,i),this.$d=(...r)=>this.$i18n.d(...r),this.$n=(...r)=>this.$i18n.n(...r),this.$tm=r=>this.$i18n.tm(r),n.__setInstance(s,this.$i18n)},mounted(){},unmounted(){const s=Bn();if(!s)throw Re(Ne.UNEXPECTED_ERROR);const o=this.$i18n;delete this.$t,delete this.$rt,delete this.$tc,delete this.$te,delete this.$d,delete this.$n,delete this.$tm,o.__disposer&&(o.__disposer(),delete o.__disposer,delete o.__extender),n.__deleteInstance(s),delete this.$i18n}}}function Fi(e,t){e.locale=t.locale||e.locale,e.fallbackLocale=t.fallbackLocale||e.fallbackLocale,e.missing=t.missing||e.missing,e.silentTranslationWarn=t.silentTranslationWarn||e.silentFallbackWarn,e.silentFallbackWarn=t.silentFallbackWarn||e.silentFallbackWarn,e.formatFallbackMessages=t.formatFallbackMessages||e.formatFallbackMessages,e.postTranslation=t.postTranslation||e.postTranslation,e.warnHtmlInMessage=t.warnHtmlInMessage||e.warnHtmlInMessage,e.escapeParameterHtml=t.escapeParameterHtml||e.escapeParameterHtml,e.sync=t.sync||e.sync,e.__composer[wa](t.pluralizationRules||e.pluralizationRules);const n=Gs(e.locale,{messages:t.messages,__i18n:t.__i18n});return Object.keys(n).forEach(s=>e.mergeLocaleMessage(s,n[s])),t.datetimeFormats&&Object.keys(t.datetimeFormats).forEach(s=>e.mergeDateTimeFormat(s,t.datetimeFormats[s])),t.numberFormats&&Object.keys(t.numberFormats).forEach(s=>e.mergeNumberFormat(s,t.numberFormats[s])),e}const em=Xt("global-vue-i18n");function tm(e={},t){const n=__VUE_I18N_LEGACY_API__&&ne(e.legacy)?e.legacy:__VUE_I18N_LEGACY_API__,s=ne(e.globalInjection)?e.globalInjection:!0,o=__VUE_I18N_LEGACY_API__&&n?!!e.allowComposition:!0,r=new Map,[i,l]=nm(e,n),a=Xt("");function c(m){return r.get(m)||null}function u(m,v){r.set(m,v)}function f(m){r.delete(m)}{const m={get mode(){return __VUE_I18N_LEGACY_API__&&n?"legacy":"composition"},get allowComposition(){return o},async install(v,...O){if(v.__VUE_I18N_SYMBOL__=a,v.provide(v.__VUE_I18N_SYMBOL__,m),Q(O[0])){const E=O[0];m.__composerExtend=E.__composerExtend,m.__vueI18nExtend=E.__vueI18nExtend}let C=null;!n&&s&&(C=fm(v,m.global)),__VUE_I18N_FULL_INSTALL__&&Qh(v,m,...O),__VUE_I18N_LEGACY_API__&&n&&v.mixin(Zh(l,l.__composer,m));const D=v.unmount;v.unmount=()=>{C&&C(),m.dispose(),D()}},get global(){return l},dispose(){i.stop()},__instances:r,__getInstance:c,__setInstance:u,__deleteInstance:f};return m}}function Ys(e={}){const t=Bn();if(t==null)throw Re(Ne.MUST_BE_CALL_SETUP_TOP);if(!t.isCE&&t.appContext.app!=null&&!t.appContext.app.__VUE_I18N_SYMBOL__)throw Re(Ne.NOT_INSTALLED);const n=sm(t),s=om(n),o=Fa(t),r=rm(e,o);if(__VUE_I18N_LEGACY_API__&&n.mode==="legacy"&&!e.__useComponent){if(!n.allowComposition)throw Re(Ne.NOT_AVAILABLE_IN_LEGACY_MODE);return cm(t,r,s,e)}if(r==="global")return Da(s,e,o),s;if(r==="parent"){let a=im(n,t,e.__useComponent);return a==null&&(a=s),a}const i=n;let l=i.__getInstance(t);if(l==null){const a=De({},e);"__i18n"in o&&(a.__i18n=o.__i18n),s&&(a.__root=s),l=oo(a),i.__composerExtend&&(l[kr]=i.__composerExtend(l)),am(i,t,l),i.__setInstance(t,l)}return l}function nm(e,t,n){const s=zi();{const o=__VUE_I18N_LEGACY_API__&&t?s.run(()=>wr(e)):s.run(()=>oo(e));if(o==null)throw Re(Ne.UNEXPECTED_ERROR);return[s,o]}}function sm(e){{const t=It(e.isCE?em:e.appContext.app.__VUE_I18N_SYMBOL__);if(!t)throw Re(e.isCE?Ne.NOT_INSTALLED_WITH_PROVIDE:Ne.UNEXPECTED_ERROR);return t}}function rm(e,t){return js(e)?"__i18n"in t?"local":"global":e.useScope?e.useScope:"local"}function om(e){return e.mode==="composition"?e.global:e.global.__composer}function im(e,t,n=!1){let s=null;const o=t.root;let r=lm(t,n);for(;r!=null;){const i=e;if(e.mode==="composition")s=i.__getInstance(r);else if(__VUE_I18N_LEGACY_API__){const l=i.__getInstance(r);l!=null&&(s=l.__composer,n&&s&&!s[Ma]&&(s=null))}if(s!=null||o===r)break;r=r.parent}return s}function lm(e,t=!1){return e==null?null:t&&e.vnode.ctx||e.parent}function am(e,t,n){cn(()=>{},t),un(()=>{const s=n;e.__deleteInstance(t);const o=s[kr];o&&(o(),delete s[kr])},t)}function cm(e,t,n,s={}){const o=t==="local",r=Br(null);if(o&&e.proxy&&!(e.proxy.$options.i18n||e.proxy.$options.__i18n))throw Re(Ne.MUST_DEFINE_I18N_OPTION_IN_ALLOW_COMPOSITION);const i=ne(s.inheritLocale)?s.inheritLocale:!K(s.locale),l=Ve(!o||i?n.locale.value:K(s.locale)?s.locale:Cn),a=Ve(!o||i?n.fallbackLocale.value:K(s.fallbackLocale)||be(s.fallbackLocale)||Q(s.fallbackLocale)||s.fallbackLocale===!1?s.fallbackLocale:l.value),c=Ve(Gs(l.value,s)),u=Ve(Q(s.datetimeFormats)?s.datetimeFormats:{[l.value]:{}}),f=Ve(Q(s.numberFormats)?s.numberFormats:{[l.value]:{}}),m=o?n.missingWarn:ne(s.missingWarn)||zt(s.missingWarn)?s.missingWarn:!0,v=o?n.fallbackWarn:ne(s.fallbackWarn)||zt(s.fallbackWarn)?s.fallbackWarn:!0,O=o?n.fallbackRoot:ne(s.fallbackRoot)?s.fallbackRoot:!0,C=!!s.fallbackFormat,D=ge(s.missing)?s.missing:null,E=ge(s.postTranslation)?s.postTranslation:null,F=o?n.warnHtmlMessage:ne(s.warnHtmlMessage)?s.warnHtmlMessage:!0,b=!!s.escapeParameter,y=o?n.modifiers:Q(s.modifiers)?s.modifiers:{},R=s.pluralRules||o&&n.pluralRules;function A(){return[l.value,a.value,c.value,u.value,f.value]}const x=Ee({get:()=>r.value?r.value.locale.value:l.value,set:_=>{r.value&&(r.value.locale.value=_),l.value=_}}),B=Ee({get:()=>r.value?r.value.fallbackLocale.value:a.value,set:_=>{r.value&&(r.value.fallbackLocale.value=_),a.value=_}}),H=Ee(()=>r.value?r.value.messages.value:c.value),ae=Ee(()=>u.value),Te=Ee(()=>f.value);function ee(){return r.value?r.value.getPostTranslationHandler():E}function me(_){r.value&&r.value.setPostTranslationHandler(_)}function ut(){return r.value?r.value.getMissingHandler():D}function ot(_){r.value&&r.value.setMissingHandler(_)}function de(_){return A(),_()}function oe(..._){return r.value?de(()=>Reflect.apply(r.value.t,null,[..._])):de(()=>"")}function se(..._){return r.value?Reflect.apply(r.value.rt,null,[..._]):""}function Ke(..._){return r.value?de(()=>Reflect.apply(r.value.d,null,[..._])):de(()=>"")}function qe(..._){return r.value?de(()=>Reflect.apply(r.value.n,null,[..._])):de(()=>"")}function Ie(_){return r.value?r.value.tm(_):{}}function Se(_,S){return r.value?r.value.te(_,S):!1}function it(_){return r.value?r.value.getLocaleMessage(_):{}}function et(_,S){r.value&&(r.value.setLocaleMessage(_,S),c.value[_]=S)}function pt(_,S){r.value&&r.value.mergeLocaleMessage(_,S)}function Ae(_){return r.value?r.value.getDateTimeFormat(_):{}}function w(_,S){r.value&&(r.value.setDateTimeFormat(_,S),u.value[_]=S)}function G(_,S){r.value&&r.value.mergeDateTimeFormat(_,S)}function j(_){return r.value?r.value.getNumberFormat(_):{}}function Y(_,S){r.value&&(r.value.setNumberFormat(_,S),f.value[_]=S)}function ie(_,S){r.value&&r.value.mergeNumberFormat(_,S)}const p={get id(){return r.value?r.value.id:-1},locale:x,fallbackLocale:B,messages:H,datetimeFormats:ae,numberFormats:Te,get inheritLocale(){return r.value?r.value.inheritLocale:i},set inheritLocale(_){r.value&&(r.value.inheritLocale=_)},get availableLocales(){return r.value?r.value.availableLocales:Object.keys(c.value)},get modifiers(){return r.value?r.value.modifiers:y},get pluralRules(){return r.value?r.value.pluralRules:R},get isGlobal(){return r.value?r.value.isGlobal:!1},get missingWarn(){return r.value?r.value.missingWarn:m},set missingWarn(_){r.value&&(r.value.missingWarn=_)},get fallbackWarn(){return r.value?r.value.fallbackWarn:v},set fallbackWarn(_){r.value&&(r.value.missingWarn=_)},get fallbackRoot(){return r.value?r.value.fallbackRoot:O},set fallbackRoot(_){r.value&&(r.value.fallbackRoot=_)},get fallbackFormat(){return r.value?r.value.fallbackFormat:C},set fallbackFormat(_){r.value&&(r.value.fallbackFormat=_)},get warnHtmlMessage(){return r.value?r.value.warnHtmlMessage:F},set warnHtmlMessage(_){r.value&&(r.value.warnHtmlMessage=_)},get escapeParameter(){return r.value?r.value.escapeParameter:b},set escapeParameter(_){r.value&&(r.value.escapeParameter=_)},t:oe,getPostTranslationHandler:ee,setPostTranslationHandler:me,getMissingHandler:ut,setMissingHandler:ot,rt:se,d:Ke,n:qe,tm:Ie,te:Se,getLocaleMessage:it,setLocaleMessage:et,mergeLocaleMessage:pt,getDateTimeFormat:Ae,setDateTimeFormat:w,mergeDateTimeFormat:G,getNumberFormat:j,setNumberFormat:Y,mergeNumberFormat:ie};function g(_){_.locale.value=l.value,_.fallbackLocale.value=a.value,Object.keys(c.value).forEach(S=>{_.mergeLocaleMessage(S,c.value[S])}),Object.keys(u.value).forEach(S=>{_.mergeDateTimeFormat(S,u.value[S])}),Object.keys(f.value).forEach(S=>{_.mergeNumberFormat(S,f.value[S])}),_.escapeParameter=b,_.fallbackFormat=C,_.fallbackRoot=O,_.fallbackWarn=v,_.missingWarn=m,_.warnHtmlMessage=F}return Tl(()=>{if(e.proxy==null||e.proxy.$i18n==null)throw Re(Ne.NOT_AVAILABLE_COMPOSITION_IN_LEGACY);const _=r.value=e.proxy.$i18n.__composer;t==="global"?(l.value=_.locale.value,a.value=_.fallbackLocale.value,c.value=_.messages.value,u.value=_.datetimeFormats.value,f.value=_.numberFormats.value):o&&g(_)}),p}const um=["locale","fallbackLocale","availableLocales"],Di=["t","rt","d","n","tm","te"];function fm(e,t){const n=Object.create(null);return um.forEach(o=>{const r=Object.getOwnPropertyDescriptor(t,o);if(!r)throw Re(Ne.UNEXPECTED_ERROR);const i=Fe(r.value)?{get(){return r.value.value},set(l){r.value.value=l}}:{get(){return r.get&&r.get()}};Object.defineProperty(n,o,i)}),e.config.globalProperties.$i18n=n,Di.forEach(o=>{const r=Object.getOwnPropertyDescriptor(t,o);if(!r||!r.value)throw Re(Ne.UNEXPECTED_ERROR);Object.defineProperty(e.config.globalProperties,`$${o}`,r)}),()=>{delete e.config.globalProperties.$i18n,Di.forEach(o=>{delete e.config.globalProperties[`$${o}`]})}}Hh();__INTLIFY_JIT_COMPILATION__?_i(Mh):_i(wh);Sh(rh);Lh(ya);if(__INTLIFY_PROD_DEVTOOLS__){const e=Mt();e.__INTLIFY__=!0,hh(e.__INTLIFY_DEVTOOLS_GLOBAL_HOOK__)}const dm={class:"nav container"},hm={class:"nav__logo"},mm={class:"nav__item"},_m={class:"nav__item"},pm={class:"nav__item"},gm={class:"nav__item"},vm={class:"nav__item"},bm={class:"nav__language"},ym={class:"language-switcher"},Em={class:"nav__cta"},Tm=["aria-expanded"],Im=Ze({__name:"AppHeader",setup(e){const t=Ve(!1),n=Ve(!1),{locale:s}=Ys(),o=()=>{t.value=window.scrollY>50},r=()=>{n.value=!n.value},i=()=>{n.value=!1},l=a=>{s.value=a,localStorage.setItem("preferred-language",a)};return cn(()=>{window.addEventListener("scroll",o)}),un(()=>{window.removeEventListener("scroll",o)}),(a,c)=>(we(),$e("header",{class:Oe(["header",{"header--scrolled":t.value}])},[L("nav",dm,[L("div",hm,[te(Le(Ue),{to:"/",class:"logo-link"},{default:xe(()=>c[2]||(c[2]=[L("span",{class:"logo-text"},"IoT",-1),L("span",{class:"logo-accent"},"Vision",-1)])),_:1,__:[2]})]),L("ul",{class:Oe(["nav__menu",{"nav__menu--open":n.value}])},[L("li",mm,[te(Le(Ue),{to:"/",class:"nav__link",onClick:i},{default:xe(()=>[lt(X(a.$t("nav.home")),1)]),_:1})]),L("li",_m,[te(Le(Ue),{to:"/solutions",class:"nav__link",onClick:i},{default:xe(()=>[lt(X(a.$t("nav.solutions")),1)]),_:1})]),L("li",pm,[te(Le(Ue),{to:"/technologies",class:"nav__link",onClick:i},{default:xe(()=>[lt(X(a.$t("nav.technologies")),1)]),_:1})]),L("li",gm,[te(Le(Ue),{to:"/about",class:"nav__link",onClick:i},{default:xe(()=>[lt(X(a.$t("nav.about")),1)]),_:1})]),L("li",vm,[te(Le(Ue),{to:"/contact",class:"nav__link",onClick:i},{default:xe(()=>[lt(X(a.$t("nav.contact")),1)]),_:1})])],2),L("div",bm,[L("div",ym,[L("button",{class:Oe(["language-btn",{active:a.$i18n.locale==="cs"}]),onClick:c[0]||(c[0]=u=>l("cs"))}," CS ",2),L("button",{class:Oe(["language-btn",{active:a.$i18n.locale==="en"}]),onClick:c[1]||(c[1]=u=>l("en"))}," EN ",2)])]),L("div",Em,[te(Le(Ue),{to:"/contact",class:"btn btn-primary"},{default:xe(()=>[lt(X(a.$t("nav.getStarted")),1)]),_:1})]),L("button",{class:"nav__toggle",onClick:r,"aria-expanded":n.value,"aria-label":"Toggle navigation menu"},c[3]||(c[3]=[L("span",{class:"nav__toggle-line"},null,-1),L("span",{class:"nav__toggle-line"},null,-1),L("span",{class:"nav__toggle-line"},null,-1)]),8,Tm)])],2))}}),$t=(e,t)=>{const n=e.__vccOpts||e;for(const[s,o]of t)n[s]=o;return n},Sm=$t(Im,[["__scopeId","data-v-0a17287d"]]),Lm={class:"footer"},Cm={class:"container"},Om={class:"footer__content"},Pm={class:"footer__brand"},Nm={class:"footer__description"},Am={class:"footer__links"},Rm={class:"footer__title"},km={class:"footer__list"},wm={class:"footer__links"},Mm={class:"footer__title"},Fm={class:"footer__contact"},Dm={class:"footer__title"},xm={class:"contact-item"},$m={href:"mailto:<EMAIL>",class:"contact-value"},Um={class:"contact-item"},Hm={href:"tel:+420123456789",class:"contact-value"},Vm={class:"contact-item"},Wm={class:"contact-value"},jm={class:"footer__bottom"},Km={class:"footer__copyright"},Bm={class:"footer__legal"},Gm={href:"#",class:"footer__link"},Ym={href:"#",class:"footer__link"},zm=Ze({__name:"AppFooter",setup(e){return Ee(()=>new Date().getFullYear()),(t,n)=>(we(),$e("footer",Lm,[L("div",Cm,[L("div",Om,[L("div",Pm,[n[0]||(n[0]=L("div",{class:"footer__logo"},[L("span",{class:"logo-text"},"IoT"),L("span",{class:"logo-accent"},"Vision")],-1)),L("p",Nm,X(t.$t("footer.description")),1),n[1]||(n[1]=Ss('<div class="footer__social" data-v-185f16f7><a href="#" class="social-link" aria-label="LinkedIn" data-v-185f16f7><svg width="24" height="24" viewBox="0 0 24 24" fill="currentColor" data-v-185f16f7><path d="M20.447 20.452h-3.554v-5.569c0-1.328-.027-3.037-1.852-3.037-1.853 0-2.136 1.445-2.136 2.939v5.667H9.351V9h3.414v1.561h.046c.477-.9 1.637-1.85 3.37-1.85 3.601 0 4.267 2.37 4.267 5.455v6.286zM5.337 7.433c-1.144 0-2.063-.926-2.063-2.065 0-1.138.92-2.063 2.063-2.063 1.14 0 2.064.925 2.064 2.063 0 1.139-.925 2.065-2.064 2.065zm1.782 13.019H3.555V9h3.564v11.452zM22.225 0H1.771C.792 0 0 .774 0 1.729v20.542C0 23.227.792 24 1.771 24h20.451C23.2 24 24 23.227 24 22.271V1.729C24 .774 23.2 0 22.222 0h.003z" data-v-185f16f7></path></svg></a><a href="#" class="social-link" aria-label="Twitter" data-v-185f16f7><svg width="24" height="24" viewBox="0 0 24 24" fill="currentColor" data-v-185f16f7><path d="M23.953 4.57a10 10 0 01-2.825.775 4.958 4.958 0 002.163-2.723c-.951.555-2.005.959-3.127 1.184a4.92 4.92 0 00-8.384 4.482C7.69 8.095 4.067 6.13 1.64 3.162a4.822 4.822 0 00-.666 2.475c0 1.71.87 3.213 2.188 4.096a4.904 4.904 0 01-2.228-.616v.06a4.923 4.923 0 003.946 4.827 4.996 4.996 0 01-2.212.085 4.936 4.936 0 004.604 3.417 9.867 9.867 0 01-6.102 2.105c-.39 0-.779-.023-1.17-.067a13.995 13.995 0 007.557 2.209c9.053 0 13.998-7.496 13.998-13.985 0-.21 0-.42-.015-.63A9.935 9.935 0 0024 4.59z" data-v-185f16f7></path></svg></a><a href="#" class="social-link" aria-label="GitHub" data-v-185f16f7><svg width="24" height="24" viewBox="0 0 24 24" fill="currentColor" data-v-185f16f7><path d="M12 0c-6.626 0-12 5.373-12 12 0 5.302 3.438 9.8 8.207 11.387.599.111.793-.261.793-.577v-2.234c-3.338.726-4.033-1.416-4.033-1.416-.546-1.387-1.333-1.756-1.333-1.756-1.089-.745.083-.729.083-.729 1.205.084 1.839 1.237 1.839 1.237 1.07 1.834 2.807 1.304 3.492.997.107-.775.418-1.305.762-1.604-2.665-.305-5.467-1.334-5.467-5.931 0-1.311.469-2.381 1.236-3.221-.124-.303-.535-1.524.117-3.176 0 0 1.008-.322 3.301 1.23.957-.266 1.983-.399 3.003-.404 1.02.005 2.047.138 3.006.404 2.291-1.552 3.297-1.23 3.297-1.23.653 1.653.242 2.874.118 3.176.77.84 1.235 1.911 1.235 3.221 0 4.609-2.807 5.624-5.479 5.921.43.372.823 1.102.823 2.222v3.293c0 .319.192.694.801.576 4.765-1.589 8.199-6.086 8.199-11.386 0-6.627-5.373-12-12-12z" data-v-185f16f7></path></svg></a></div>',1))]),L("div",Am,[L("h4",Rm,X(t.$t("footer.quickLinks")),1),L("ul",km,[L("li",null,[te(Le(Ue),{to:"/",class:"footer__link"},{default:xe(()=>[lt(X(t.$t("nav.home")),1)]),_:1})]),L("li",null,[te(Le(Ue),{to:"/solutions",class:"footer__link"},{default:xe(()=>[lt(X(t.$t("nav.solutions")),1)]),_:1})]),L("li",null,[te(Le(Ue),{to:"/technologies",class:"footer__link"},{default:xe(()=>[lt(X(t.$t("nav.technologies")),1)]),_:1})]),L("li",null,[te(Le(Ue),{to:"/about",class:"footer__link"},{default:xe(()=>[lt(X(t.$t("nav.about")),1)]),_:1})]),L("li",null,[te(Le(Ue),{to:"/contact",class:"footer__link"},{default:xe(()=>[lt(X(t.$t("nav.contact")),1)]),_:1})])])]),L("div",wm,[L("h4",Mm,X(t.$t("footer.solutions")),1),n[2]||(n[2]=Ss('<ul class="footer__list" data-v-185f16f7><li data-v-185f16f7><a href="#" class="footer__link" data-v-185f16f7>IoT Development</a></li><li data-v-185f16f7><a href="#" class="footer__link" data-v-185f16f7>Smart Home Solutions</a></li><li data-v-185f16f7><a href="#" class="footer__link" data-v-185f16f7>Hardware Design</a></li><li data-v-185f16f7><a href="#" class="footer__link" data-v-185f16f7>Cloud Integration</a></li><li data-v-185f16f7><a href="#" class="footer__link" data-v-185f16f7>Consulting</a></li></ul>',1))]),L("div",Fm,[L("h4",Dm,X(t.$t("nav.contact")),1),L("div",xm,[n[3]||(n[3]=L("span",{class:"contact-label"},"Email:",-1)),L("a",$m,X(t.$t("contact.info.email")),1)]),L("div",Um,[n[4]||(n[4]=L("span",{class:"contact-label"},"Phone:",-1)),L("a",Hm,X(t.$t("contact.info.phone")),1)]),L("div",Vm,[n[5]||(n[5]=L("span",{class:"contact-label"},"Address:",-1)),L("span",Wm,X(t.$t("contact.info.address")),1)])])]),L("div",jm,[L("div",Km,[L("p",null,X(t.$t("footer.copyright")),1)]),L("div",Bm,[L("a",Gm,X(t.$t("footer.privacy")),1),L("a",Ym,X(t.$t("footer.terms")),1)])])])]))}}),Xm=$t(zm,[["__scopeId","data-v-185f16f7"]]),qm={class:"particle-background"},Jm=80,xi=150,fr=100,Qm=Ze({__name:"ParticleBackground",setup(e){const t=Ve(null);let n=null,s=[];const o=["#00FFFF","#00FF7F","#FF00FF"];let r={x:0,y:0},i,l;const a=E=>({x:Math.random()*i.width,y:Math.random()*i.height,vx:(Math.random()-.5)*.5,vy:(Math.random()-.5)*.5,size:Math.random()*2+1,opacity:Math.random()*.5+.3,color:o[Math.floor(Math.random()*o.length)],connections:[]}),c=()=>{s=[];for(let E=0;E<Jm;E++)s.push(a())},u=E=>{E.x+=E.vx,E.y+=E.vy;const F=r.x-E.x,b=r.y-E.y,y=Math.sqrt(F*F+b*b);if(y<fr){const R=(fr-y)/fr;E.vx+=F/y*R*.01,E.vy+=b/y*R*.01}(E.x<0||E.x>i.width)&&(E.vx*=-1,E.x=Math.max(0,Math.min(i.width,E.x))),(E.y<0||E.y>i.height)&&(E.vy*=-1,E.y=Math.max(0,Math.min(i.height,E.y))),E.vx*=.99,E.vy*=.99,E.opacity+=(Math.random()-.5)*.01,E.opacity=Math.max(.1,Math.min(.8,E.opacity))},f=E=>{l.save(),l.globalAlpha=E.opacity,l.fillStyle=E.color,l.shadowBlur=10,l.shadowColor=E.color,l.beginPath(),l.arc(E.x,E.y,E.size,0,Math.PI*2),l.fill(),l.restore()},m=()=>{for(let E=0;E<s.length;E++)for(let F=E+1;F<s.length;F++){const b=s[E].x-s[F].x,y=s[E].y-s[F].y,R=Math.sqrt(b*b+y*y);if(R<xi){const A=(1-R/xi)*.3;l.save(),l.globalAlpha=A,l.strokeStyle=s[E].color,l.lineWidth=.5,l.shadowBlur=5,l.shadowColor=s[E].color,l.beginPath(),l.moveTo(s[E].x,s[E].y),l.lineTo(s[F].x,s[F].y),l.stroke(),l.restore()}}},v=()=>{l.clearRect(0,0,i.width,i.height),s.forEach(E=>{u(E),f(E)}),m(),n=requestAnimationFrame(v)},O=()=>{i&&(i.width=window.innerWidth,i.height=window.innerHeight,c())},C=E=>{r.x=E.clientX,r.y=E.clientY},D=()=>{O()};return cn(()=>{i=t.value,l=i.getContext("2d"),O(),c(),v(),window.addEventListener("resize",D),window.addEventListener("mousemove",C)}),un(()=>{n&&cancelAnimationFrame(n),window.removeEventListener("resize",D),window.removeEventListener("mousemove",C)}),(E,F)=>(we(),$e("div",qm,[L("canvas",{ref_key:"canvasRef",ref:t,class:"particle-canvas"},null,512)]))}}),Zm=$t(Qm,[["__scopeId","data-v-765ad019"]]),e_={id:"app",class:"app"},t_={class:"main-content"},n_=Ze({__name:"App",setup(e){return(t,n)=>(we(),$e("div",e_,[te(Zm),te(Sm),L("main",t_,[te(Le(ra))]),te(Xm)]))}}),s_=$t(n_,[["__scopeId","data-v-e8c19943"]]),r_="modulepreload",o_=function(e){return"/"+e},$i={},as=function(t,n,s){let o=Promise.resolve();if(n&&n.length>0){let i=function(c){return Promise.all(c.map(u=>Promise.resolve(u).then(f=>({status:"fulfilled",value:f}),f=>({status:"rejected",reason:f}))))};document.getElementsByTagName("link");const l=document.querySelector("meta[property=csp-nonce]"),a=(l==null?void 0:l.nonce)||(l==null?void 0:l.getAttribute("nonce"));o=i(n.map(c=>{if(c=o_(c),c in $i)return;$i[c]=!0;const u=c.endsWith(".css"),f=u?'[rel="stylesheet"]':"";if(document.querySelector(`link[href="${c}"]${f}`))return;const m=document.createElement("link");if(m.rel=u?"stylesheet":r_,u||(m.as="script"),m.crossOrigin="",m.href=c,a&&m.setAttribute("nonce",a),document.head.appendChild(m),u)return new Promise((v,O)=>{m.addEventListener("load",v),m.addEventListener("error",()=>O(new Error(`Unable to preload CSS for ${c}`)))})}))}function r(i){const l=new Event("vite:preloadError",{cancelable:!0});if(l.payload=i,window.dispatchEvent(l),!l.defaultPrevented)throw i}return o.then(i=>{for(const l of i||[])l.status==="rejected"&&r(l.reason);return t().catch(r)})},i_={class:"hero"},l_={class:"hero__background"},a_={class:"container hero__container"},c_={class:"hero__content"},u_={class:"badge-text"},f_=["innerHTML"],d_={class:"stat"},h_={class:"stat-label"},m_={class:"stat"},__={class:"stat-label"},p_={class:"stat"},g_={class:"stat-label"},v_={class:"visual-container"},b_={class:"floating-elements"},y_={class:"central-hub"},E_={class:"scroll-text"},T_=Ze({__name:"HeroSection",setup(e){const t=Ve(null),n=Ve(!1);let s=null,o,r;const i=()=>{console.log("Playing demo...")},l=()=>{if(!t.value)return;o=t.value,r=o.getContext("2d");const a=()=>{o.width=o.offsetWidth,o.height=o.offsetHeight};a(),window.addEventListener("resize",a);const c=[],u=()=>({x:-20,y:Math.random()*o.height,vx:1+Math.random()*2,vy:(Math.random()-.5)*.5,opacity:.3+Math.random()*.4});for(let m=0;m<5;m++)c.push(u());const f=()=>{r.clearRect(0,0,o.width,o.height),c.forEach((m,v)=>{if(m.x+=m.vx,m.y+=m.vy,m.x>o.width+20){c[v]=u();return}r.save(),r.globalAlpha=m.opacity,r.fillStyle="#00FFFF",r.shadowBlur=10,r.shadowColor="#00FFFF",r.beginPath(),r.arc(m.x,m.y,2,0,Math.PI*2),r.fill(),r.strokeStyle="#00FFFF",r.lineWidth=1,r.beginPath(),r.moveTo(m.x-20,m.y),r.lineTo(m.x,m.y),r.stroke(),r.restore()}),s=requestAnimationFrame(f)};f()};return cn(()=>{setTimeout(()=>{n.value=!0},100),setTimeout(()=>{l()},500)}),un(()=>{s&&cancelAnimationFrame(s)}),(a,c)=>(we(),$e("section",i_,[L("div",l_,[L("canvas",{ref_key:"heroCanvasRef",ref:t,class:"hero__canvas"},null,512)]),L("div",a_,[L("div",c_,[L("div",{class:Oe(["hero__badge",{"animate-fade-in":n.value}])},[L("span",u_,X(a.$t("hero.badge")),1)],2),L("h1",{class:Oe(["hero__title",{"animate-slide-in-up delay-200":n.value}])},[L("span",{innerHTML:a.$t("hero.title",{highlight:`<span class='title-highlight'>${a.$t("hero.titleHighlight")}</span>`})},null,8,f_)],2),L("p",{class:Oe(["hero__subtitle",{"animate-slide-in-up delay-300":n.value}])},X(a.$t("hero.subtitle")),3),L("div",{class:Oe(["hero__actions",{"animate-slide-in-up delay-500":n.value}])},[te(Le(Ue),{to:"/contact",class:"btn btn-primary hero__cta"},{default:xe(()=>[L("span",null,X(a.$t("hero.exploreSolutions")),1),c[0]||(c[0]=L("svg",{class:"btn-icon",width:"20",height:"20",viewBox:"0 0 24 24",fill:"none"},[L("path",{d:"M5 12h14M12 5l7 7-7 7",stroke:"currentColor","stroke-width":"2","stroke-linecap":"round","stroke-linejoin":"round"})],-1))]),_:1,__:[0]}),L("button",{class:"btn btn-ghost hero__demo",onClick:i},[c[1]||(c[1]=L("svg",{class:"btn-icon",width:"20",height:"20",viewBox:"0 0 24 24",fill:"none"},[L("polygon",{points:"5,3 19,12 5,21",fill:"currentColor"})],-1)),L("span",null,X(a.$t("hero.watchDemo")),1)])],2),L("div",{class:Oe(["hero__stats",{"animate-fade-in delay-700":n.value}])},[L("div",d_,[c[2]||(c[2]=L("div",{class:"stat-number"},"500+",-1)),L("div",h_,X(a.$t("hero.stats.projects")),1)]),L("div",m_,[c[3]||(c[3]=L("div",{class:"stat-number"},"50M+",-1)),L("div",__,X(a.$t("hero.stats.devices")),1)]),L("div",p_,[c[4]||(c[4]=L("div",{class:"stat-number"},"99.9%",-1)),L("div",g_,X(a.$t("hero.stats.uptime")),1)])],2)]),L("div",{class:Oe(["hero__visual",{"animate-scale-in delay-400":n.value}])},[L("div",v_,[L("div",b_,[L("div",{class:Oe(["element element--1",{"animate-float":n.value}])},null,2),L("div",{class:Oe(["element element--2",{"animate-float delay-100":n.value}])},null,2),L("div",{class:Oe(["element element--3",{"animate-float delay-200":n.value}])},null,2)]),L("div",y_,[L("div",{class:Oe(["hub-core",{"animate-pulse-neon":n.value}])},null,2),c[5]||(c[5]=L("div",{class:"hub-rings"},[L("div",{class:"ring ring--1"}),L("div",{class:"ring ring--2"}),L("div",{class:"ring ring--3"})],-1))])])],2)]),L("div",{class:Oe(["hero__scroll-indicator",{"animate-fade-in delay-1000":n.value}])},[L("div",E_,X(a.$t("hero.scrollToExplore")),1),c[6]||(c[6]=L("div",{class:"scroll-arrow"},null,-1))],2)]))}}),I_=$t(T_,[["__scopeId","data-v-e9a0ab5b"]]),S_={class:"features section"},L_={class:"container"},C_={class:"features__header"},O_={class:"features__title"},P_={class:"features__subtitle"},N_={class:"features__grid"},A_={class:"feature-card__icon"},R_={class:"icon-placeholder"},k_={class:"feature-card__title"},w_={class:"feature-card__description"},M_={class:"feature-card__stats"},F_={class:"stat"},D_={class:"stat-value"},x_={class:"stat-label"},$_=Ze({__name:"FeaturesSection",setup(e){const t=Ve(!1),n=[{id:1,titleKey:"features.smartConnectivity.title",descriptionKey:"features.smartConnectivity.description",iconText:"⚡",stat:{value:"<1ms",label:"Latency"}},{id:2,titleKey:"features.realTimeAnalytics.title",descriptionKey:"features.realTimeAnalytics.description",iconText:"🧠",stat:{value:"95%",label:"Accuracy"}},{id:3,titleKey:"features.secureInfrastructure.title",descriptionKey:"features.secureInfrastructure.description",iconText:"🛡️",stat:{value:"256-bit",label:"Encryption"}},{id:4,titleKey:"features.scalablePlatform.title",descriptionKey:"features.scalablePlatform.description",iconText:"📈",stat:{value:"10M+",label:"Devices"}}];return cn(()=>{const s=new IntersectionObserver(r=>{r.forEach(i=>{i.isIntersecting&&(t.value=!0)})},{threshold:.1}),o=document.querySelector(".features");o&&s.observe(o)}),(s,o)=>(we(),$e("section",S_,[L("div",L_,[L("div",C_,[L("h2",O_,X(s.$t("features.title")),1),L("p",P_,X(s.$t("features.subtitle")),1)]),L("div",N_,[(we(),$e(Xe,null,us(n,(r,i)=>L("div",{key:r.id,class:Oe(["feature-card",{"animate-scale-in":t.value}]),style:Zn({animationDelay:`${i*.1}s`})},[L("div",A_,[L("div",R_,X(r.iconText),1)]),L("h3",k_,X(s.$t(r.titleKey)),1),L("p",w_,X(s.$t(r.descriptionKey)),1),L("div",M_,[L("div",F_,[L("span",D_,X(r.stat.value),1),L("span",x_,X(r.stat.label),1)])])],6)),64))])])]))}}),U_=$t($_,[["__scopeId","data-v-7109a76f"]]),H_={class:"technologies-preview section"},V_={class:"container"},W_={class:"technologies__header"},j_={class:"technologies__title"},K_={class:"technologies__subtitle"},B_={class:"technologies__content"},G_={class:"tech-showcase"},Y_={class:"tech-visual"},z_={class:"tech-nodes"},X_=["onClick"],q_={class:"node-icon"},J_={class:"node-label"},Q_={class:"tech-details"},Z_={key:0,class:"tech-card"},ep={class:"tech-card__title"},tp={class:"tech-card__description"},np={class:"tech-specs"},sp={class:"spec-label"},rp={class:"spec-value"},op={class:"tech-features"},ip={class:"features-list"},lp=Ze({__name:"TechnologiesPreview",setup(e){const t=Ve(null),n=Ve(0),s=[{id:1,x:20,y:30,icon:"🔗",label:"Connectivity"},{id:2,x:50,y:20,icon:"🧠",label:"AI/ML"},{id:3,x:80,y:35,icon:"☁️",label:"Cloud"},{id:4,x:30,y:70,icon:"🔒",label:"Security"},{id:5,x:70,y:75,icon:"📊",label:"Analytics"}],o=[{titleKey:"technologies.iot.title",descriptionKey:"technologies.iot.description",specs:[{label:"Protocols",value:"15+"},{label:"Range",value:"10km+"},{label:"Latency",value:"<1ms"}],features:["Auto-discovery and pairing","Mesh networking capabilities","Adaptive protocol switching","Edge-to-cloud connectivity"]},{titleKey:"technologies.ai.title",descriptionKey:"technologies.ai.description",specs:[{label:"Models",value:"50+"},{label:"Accuracy",value:"99.5%"},{label:"Processing",value:"Real-time"}],features:["Predictive analytics","Anomaly detection","Natural language processing","Computer vision integration"]},{titleKey:"technologies.cloud.title",descriptionKey:"technologies.cloud.description",specs:[{label:"Uptime",value:"99.99%"},{label:"Regions",value:"25+"},{label:"Scale",value:"Unlimited"}],features:["Auto-scaling infrastructure","Global edge deployment","Multi-cloud support","Disaster recovery"]},{titleKey:"technologies.security.title",descriptionKey:"technologies.security.description",specs:[{label:"Encryption",value:"AES-256"},{label:"Compliance",value:"SOC2, ISO27001"},{label:"Authentication",value:"Multi-factor"}],features:["Zero-trust architecture","Blockchain verification","Threat intelligence","Automated security updates"]}],r=Ee(()=>o[n.value]),i=c=>{n.value=c};let l=null;const a=()=>{if(!t.value)return;const c=t.value,u=c.getContext("2d"),f=()=>{c.width=c.offsetWidth,c.height=c.offsetHeight};f(),window.addEventListener("resize",f);const m=()=>{u.clearRect(0,0,c.width,c.height),u.strokeStyle="rgba(0, 255, 255, 0.3)",u.lineWidth=1;for(let v=0;v<s.length;v++)for(let O=v+1;O<s.length;O++){const C=s[v],D=s[O],E=C.x/100*c.width,F=C.y/100*c.height,b=D.x/100*c.width,y=D.y/100*c.height;u.beginPath(),u.moveTo(E,F),u.lineTo(b,y),u.stroke()}l=requestAnimationFrame(m)};m()};return cn(()=>{setTimeout(()=>{a()},500);const c=setInterval(()=>{n.value=(n.value+1)%o.length},5e3);un(()=>{clearInterval(c),l&&cancelAnimationFrame(l)})}),(c,u)=>(we(),$e("section",H_,[L("div",V_,[L("div",W_,[L("h2",j_,X(c.$t("technologies.title")),1),L("p",K_,X(c.$t("technologies.subtitle")),1)]),L("div",B_,[L("div",G_,[L("div",Y_,[L("canvas",{ref_key:"techCanvasRef",ref:t,class:"tech-canvas"},null,512),L("div",z_,[(we(),$e(Xe,null,us(s,(f,m)=>L("div",{key:f.id,class:Oe(["tech-node",{active:n.value===m}]),style:Zn({left:f.x+"%",top:f.y+"%",animationDelay:`${m*.2}s`}),onClick:v=>i(m)},[L("div",q_,X(f.icon),1),L("div",J_,X(f.label),1)],14,X_)),64))])]),L("div",Q_,[r.value?(we(),$e("div",Z_,[L("h3",ep,X(c.$t(r.value.titleKey)),1),L("p",tp,X(c.$t(r.value.descriptionKey)),1),L("div",np,[(we(!0),$e(Xe,null,us(r.value.specs,f=>(we(),$e("div",{key:f.label,class:"spec-item"},[L("span",sp,X(f.label),1),L("span",rp,X(f.value),1)]))),128))]),L("div",op,[u[0]||(u[0]=L("h4",{class:"features-title"},"Key Features",-1)),L("ul",ip,[(we(!0),$e(Xe,null,us(r.value.features,f=>(we(),$e("li",{key:f},X(f),1))),128))])])])):gu("",!0)])]),u[1]||(u[1]=Ss('<div class="tech-stats" data-v-7a97131a><div class="stat-card" data-v-7a97131a><div class="stat-number" data-v-7a97131a>15+</div><div class="stat-label" data-v-7a97131a>Technologies</div></div><div class="stat-card" data-v-7a97131a><div class="stat-number" data-v-7a97131a>99.9%</div><div class="stat-label" data-v-7a97131a>Reliability</div></div><div class="stat-card" data-v-7a97131a><div class="stat-number" data-v-7a97131a>24/7</div><div class="stat-label" data-v-7a97131a>Support</div></div></div>',1))])])]))}}),ap=$t(lp,[["__scopeId","data-v-7a97131a"]]),cp={class:"cta-section section"},up={class:"container"},fp={class:"cta-content"},dp={class:"cta-title"},hp={class:"cta-subtitle"},mp={class:"cta-actions"},_p=Ze({__name:"CTASection",setup(e){return(t,n)=>(we(),$e("section",cp,[L("div",up,[L("div",fp,[L("h2",dp,X(t.$t("cta.title")),1),L("p",hp,X(t.$t("cta.subtitle")),1),L("div",mp,[te(Le(Ue),{to:"/contact",class:"btn btn-primary cta-primary"},{default:xe(()=>[L("span",null,X(t.$t("cta.button")),1),n[0]||(n[0]=L("svg",{class:"btn-icon",width:"20",height:"20",viewBox:"0 0 24 24",fill:"none"},[L("path",{d:"M5 12h14M12 5l7 7-7 7",stroke:"currentColor","stroke-width":"2","stroke-linecap":"round","stroke-linejoin":"round"})],-1))]),_:1,__:[0]}),te(Le(Ue),{to:"/solutions",class:"btn btn-outline cta-secondary"},{default:xe(()=>[L("span",null,X(t.$t("cta.contact")),1)]),_:1})]),n[1]||(n[1]=Ss('<div class="cta-features" data-v-53a875b0><div class="feature-item" data-v-53a875b0><span class="feature-icon" data-v-53a875b0>✓</span><span data-v-53a875b0>Free Consultation</span></div><div class="feature-item" data-v-53a875b0><span class="feature-icon" data-v-53a875b0>✓</span><span data-v-53a875b0>30-Day Trial</span></div><div class="feature-item" data-v-53a875b0><span class="feature-icon" data-v-53a875b0>✓</span><span data-v-53a875b0>24/7 Support</span></div></div>',1))])])]))}}),pp=$t(_p,[["__scopeId","data-v-53a875b0"]]),gp={class:"home-view"},vp=Ze({__name:"HomeView",setup(e){return(t,n)=>(we(),$e("div",gp,[te(I_),te(U_),te(ap),te(pp)]))}}),bp=$t(vp,[["__scopeId","data-v-4b95c547"]]),yp=sd({history:Mf("/"),routes:[{path:"/",name:"home",component:bp},{path:"/solutions",name:"solutions",component:()=>as(()=>import("./SolutionsView-BZcr96vX.js"),__vite__mapDeps([0,1]))},{path:"/technologies",name:"technologies",component:()=>as(()=>import("./TechnologiesView-CghOwnN5.js"),__vite__mapDeps([2,3]))},{path:"/about",name:"about",component:()=>as(()=>import("./AboutView-DGSopZJK.js"),__vite__mapDeps([4,5]))},{path:"/contact",name:"contact",component:()=>as(()=>import("./ContactView-BkdETCjs.js"),__vite__mapDeps([6,7]))}],scrollBehavior(e,t,n){return n||{top:0}}}),Ep={home:"Home",solutions:"Solutions",technologies:"Technologies",about:"About",contact:"Contact",getStarted:"Get Started"},Tp={badge:"Next Generation IoT",title:"Welcome to the {highlight} of Connected Intelligence",titleHighlight:"Future",subtitle:"Transform your world with cutting-edge IoT solutions that bridge the gap between imagination and reality. Where every device becomes intelligent, every connection meaningful, and every data point a step toward tomorrow.",exploreSolutions:"Explore Solutions",watchDemo:"Watch Demo",scrollToExplore:"Scroll to explore",stats:{projects:"Projects Delivered",devices:"Devices Connected",uptime:"Uptime Guaranteed"}},Ip={title:"Revolutionary IoT Solutions",subtitle:"Discover how our advanced technology transforms industries and creates intelligent ecosystems",smartConnectivity:{title:"Smart Connectivity",description:"Seamless device integration with ultra-low latency and 99.9% reliability"},realTimeAnalytics:{title:"Real-time Analytics",description:"Advanced AI-powered insights that turn data into actionable intelligence"},secureInfrastructure:{title:"Secure Infrastructure",description:"Enterprise-grade security with end-to-end encryption and threat protection"},scalablePlatform:{title:"Scalable Platform",description:"Cloud-native architecture that grows with your business needs"}},Sp={title:"Cutting-Edge Technologies",subtitle:"Powered by the latest innovations in IoT, AI, and cloud computing",iot:{title:"IoT Ecosystem",description:"Complete device management and orchestration platform"},ai:{title:"Artificial Intelligence",description:"Machine learning algorithms for predictive analytics"},cloud:{title:"Cloud Infrastructure",description:"Scalable and reliable cloud-native solutions"},security:{title:"Cybersecurity",description:"Advanced security protocols and threat detection"}},Lp={title:"Industry Solutions",subtitle:"Tailored IoT solutions for every industry vertical",smartCities:{title:"Smart Cities",description:"Transform urban infrastructure with intelligent monitoring, traffic optimization, and energy management systems.",features:["Traffic Flow Optimization","Smart Lighting Systems","Environmental Monitoring","Waste Management","Public Safety Enhancement"]},manufacturing:{title:"Industrial Manufacturing",description:"Revolutionize production with predictive maintenance, quality control, and supply chain optimization.",features:["Predictive Maintenance","Quality Assurance","Supply Chain Tracking","Energy Optimization","Worker Safety Monitoring"]},healthcare:{title:"Healthcare & Medical",description:"Enhance patient care with remote monitoring, asset tracking, and intelligent medical devices.",features:["Patient Monitoring","Medical Asset Tracking","Environmental Control","Emergency Response","Data Analytics"]},agriculture:{title:"Smart Agriculture",description:"Optimize crop yields with precision farming, soil monitoring, and automated irrigation systems.",features:["Soil Monitoring","Weather Stations","Automated Irrigation","Crop Health Analysis","Livestock Tracking"]}},Cp={title:"About IoTVision",subtitle:"Leading the future of connected intelligence",mission:{title:"Our Mission",description:"To democratize IoT technology and make intelligent connectivity accessible to businesses of all sizes, driving innovation and efficiency across industries."},vision:{title:"Our Vision",description:"A world where every device is intelligent, every connection is meaningful, and every data point contributes to a smarter, more sustainable future."},values:{title:"Our Values",innovation:{title:"Innovation",description:"Pushing the boundaries of what's possible with cutting-edge technology"},reliability:{title:"Reliability",description:"Building robust solutions that businesses can depend on"},security:{title:"Security",description:"Protecting data and privacy with enterprise-grade security"},sustainability:{title:"Sustainability",description:"Creating solutions that contribute to a more sustainable future"}},team:{title:"Expert Team",description:"Our team of IoT specialists, data scientists, and engineers brings decades of combined experience in creating innovative connected solutions."}},Op={title:"Get in Touch",subtitle:"Ready to transform your business with IoT? Let's discuss your project.",form:{name:"Full Name",email:"Email Address",company:"Company Name",phone:"Phone Number",subject:"Subject",message:"Message",send:"Send Message",sending:"Sending...",success:"Message sent successfully!",error:"Failed to send message. Please try again."},info:{address:"Prague, Czech Republic",email:"<EMAIL>",phone:"+*********** 789",hours:"Mon-Fri: 9:00 - 18:00"}},Pp={title:"Ready to Start Your IoT Journey?",subtitle:"Join thousands of businesses already transforming with our IoT solutions",button:"Get Started Today",contact:"Contact Us"},Np={description:"Leading provider of innovative IoT solutions for the connected world.",quickLinks:"Quick Links",solutions:"Solutions",company:"Company",legal:"Legal",privacy:"Privacy Policy",terms:"Terms of Service",cookies:"Cookie Policy",copyright:"© 2024 IoTVision. All rights reserved.",madeWith:"Made with ❤️ using Vue.js 3 and modern web technologies."},Ap={nav:Ep,hero:Tp,features:Ip,technologies:Sp,solutions:Lp,about:Cp,contact:Op,cta:Pp,footer:Np},Rp={home:"Domů",solutions:"Řešení",technologies:"Technologie",about:"O nás",contact:"Kontakt",getStarted:"Začít"},kp={badge:"IoT Nové Generace",title:"Vítejte v {highlight} propojené inteligence",titleHighlight:"budoucnosti",subtitle:"Transformujte svět pomocí špičkových IoT řešení, která překlenují propast mezi představivostí a realitou. Kde se každé zařízení stává inteligentním, každé spojení smysluplným a každý datový bod krokem k zítřku.",exploreSolutions:"Prozkoumat řešení",watchDemo:"Sledovat demo",scrollToExplore:"Rolujte pro prozkoumání",stats:{projects:"Dokončených projektů",devices:"Připojených zařízení",uptime:"Garantovaná dostupnost"}},wp={title:"Revoluční IoT řešení",subtitle:"Objevte, jak naše pokročilá technologie transformuje odvětví a vytváří inteligentní ekosystémy",smartConnectivity:{title:"Chytré propojení",description:"Bezproblémová integrace zařízení s ultra-nízkou latencí a 99,9% spolehlivostí"},realTimeAnalytics:{title:"Analýzy v reálném čase",description:"Pokročilé poznatky poháněné AI, které mění data na užitečnou inteligenci"},secureInfrastructure:{title:"Bezpečná infrastruktura",description:"Podniková bezpečnost s end-to-end šifrováním a ochranou před hrozbami"},scalablePlatform:{title:"Škálovatelná platforma",description:"Cloud-native architektura, která roste s potřebami vašeho podnikání"}},Mp={title:"Špičkové technologie",subtitle:"Poháněno nejnovějšími inovacemi v IoT, AI a cloud computingu",iot:{title:"IoT ekosystém",description:"Kompletní platforma pro správu a orchestraci zařízení"},ai:{title:"Umělá inteligence",description:"Algoritmy strojového učení pro prediktivní analýzy"},cloud:{title:"Cloudová infrastruktura",description:"Škálovatelná a spolehlivá cloud-native řešení"},security:{title:"Kybernetická bezpečnost",description:"Pokročilé bezpečnostní protokoly a detekce hrozeb"}},Fp={title:"Odvětvová řešení",subtitle:"Přizpůsobená IoT řešení pro každé odvětví",smartCities:{title:"Chytrá města",description:"Transformujte městskou infrastrukturu pomocí inteligentního monitoringu, optimalizace dopravy a systémů řízení energie.",features:["Optimalizace dopravního toku","Chytré osvětlovací systémy","Monitorování životního prostředí","Správa odpadu","Zvýšení veřejné bezpečnosti"]},manufacturing:{title:"Průmyslová výroba",description:"Revolucionalizujte výrobu pomocí prediktivní údržby, kontroly kvality a optimalizace dodavatelského řetězce.",features:["Prediktivní údržba","Zajištění kvality","Sledování dodavatelského řetězce","Optimalizace energie","Monitorování bezpečnosti pracovníků"]},healthcare:{title:"Zdravotnictví a medicína",description:"Vylepšete péči o pacienty pomocí vzdáleného monitoringu, sledování majetku a inteligentních zdravotnických zařízení.",features:["Monitorování pacientů","Sledování zdravotnického majetku","Kontrola prostředí","Nouzová reakce","Analýza dat"]},agriculture:{title:"Chytré zemědělství",description:"Optimalizujte výnosy plodin pomocí precizního zemědělství, monitoringu půdy a automatizovaných zavlažovacích systémů.",features:["Monitorování půdy","Meteorologické stanice","Automatizované zavlažování","Analýza zdraví plodin","Sledování hospodářských zvířat"]}},Dp={title:"O společnosti IoTVision",subtitle:"Vedeme budoucnost propojené inteligence",mission:{title:"Naše mise",description:"Demokratizovat IoT technologie a zpřístupnit inteligentní konektivitu podnikům všech velikostí, podporovat inovace a efektivitu napříč odvětvími."},vision:{title:"Naše vize",description:"Svět, kde je každé zařízení inteligentní, každé spojení smysluplné a každý datový bod přispívá k chytřejší, udržitelnější budoucnosti."},values:{title:"Naše hodnoty",innovation:{title:"Inovace",description:"Posouvání hranic možného pomocí špičkových technologií"},reliability:{title:"Spolehlivost",description:"Budování robustních řešení, na která se podniky mohou spolehnout"},security:{title:"Bezpečnost",description:"Ochrana dat a soukromí pomocí podnikové bezpečnosti"},sustainability:{title:"Udržitelnost",description:"Vytváření řešení, která přispívají k udržitelnější budoucnosti"}},team:{title:"Expertní tým",description:"Náš tým IoT specialistů, datových vědců a inženýrů přináší desetiletí kombinovaných zkušeností ve vytváření inovativních propojených řešení."}},xp={title:"Kontaktujte nás",subtitle:"Připraveni transformovat své podnikání pomocí IoT? Pojďme diskutovat o vašem projektu.",form:{name:"Celé jméno",email:"E-mailová adresa",company:"Název společnosti",phone:"Telefonní číslo",subject:"Předmět",message:"Zpráva",send:"Odeslat zprávu",sending:"Odesílání...",success:"Zpráva byla úspěšně odeslána!",error:"Nepodařilo se odeslat zprávu. Zkuste to prosím znovu."},info:{address:"Praha, Česká republika",email:"<EMAIL>",phone:"+*********** 789",hours:"Po-Pá: 9:00 - 18:00"}},$p={title:"Připraveni začít svou IoT cestu?",subtitle:"Připojte se k tisícům podniků, které se již transformují pomocí našich IoT řešení",button:"Začít ještě dnes",contact:"Kontaktujte nás"},Up={description:"Přední poskytovatel inovativních IoT řešení pro propojený svět.",quickLinks:"Rychlé odkazy",solutions:"Řešení",company:"Společnost",legal:"Právní",privacy:"Zásady ochrany osobních údajů",terms:"Podmínky služby",cookies:"Zásady cookies",copyright:"© 2024 IoTVision. Všechna práva vyhrazena.",madeWith:"Vytvořeno s ❤️ pomocí Vue.js 3 a moderních webových technologií."},Hp={nav:Rp,hero:kp,features:wp,technologies:Mp,solutions:Fp,about:Dp,contact:xp,cta:$p,footer:Up},Vp=()=>{const e=localStorage.getItem("preferred-language");return e&&["en","cs"].includes(e)?e:(navigator.language||navigator.userLanguage).toLowerCase().startsWith("cs")?"cs":"en"},Wp=tm({legacy:!1,locale:Vp(),fallbackLocale:"en",messages:{en:Ap,cs:Hp}}),zs=Qu(s_);zs.use(nf());zs.use(yp);zs.use(Wp);zs.mount("#app");export{Xe as F,Ue as R,$t as _,L as a,te as b,$e as c,Ze as d,lt as e,Ve as f,Ee as g,gu as h,Ss as i,Ds as j,Gp as k,jp as l,Bp as m,Oe as n,we as o,us as r,X as t,Le as u,Kp as v,xe as w};
