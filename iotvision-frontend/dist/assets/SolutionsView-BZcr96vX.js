import{d,c as n,a as t,t as s,F as c,r,b as _,w as p,e as h,u as m,R as v,o as a,_ as y}from"./index-DNqMNZKj.js";const f={class:"solutions-view"},g={class:"solutions-hero section"},b={class:"container"},K={class:"hero-content"},k={class:"hero-title"},E={class:"hero-subtitle"},S={class:"solutions-grid section"},w={class:"container"},C={class:"solutions-list"},V={class:"solution-icon"},I={class:"solution-title"},$={class:"solution-description"},B={class:"solution-features"},F={class:"solution-stats"},L={class:"stat"},N={class:"stat-value"},R={class:"stat-label"},T=d({__name:"SolutionsView",setup(z){const u=[{id:1,titleKey:"solutions.manufacturing.title",icon:"🏭",descriptionKey:"solutions.manufacturing.description",features:["Predictive maintenance","Quality control automation","Energy optimization","Supply chain visibility"],stat:{value:"40%",label:"Efficiency Increase"}},{id:2,titleKey:"solutions.smartCities.title",icon:"🏙️",descriptionKey:"solutions.smartCities.description",features:["Traffic flow optimization","Smart lighting systems","Waste management","Environmental monitoring"],stat:{value:"30%",label:"Energy Savings"}},{id:3,titleKey:"solutions.healthcare.title",icon:"🏥",descriptionKey:"solutions.healthcare.description",features:["Patient monitoring","Asset tracking","Environmental control","Emergency response"],stat:{value:"50%",label:"Response Time Improvement"}},{id:4,titleKey:"solutions.agriculture.title",icon:"🌾",descriptionKey:"solutions.agriculture.description",features:["Soil monitoring","Automated irrigation","Crop health tracking","Weather integration"],stat:{value:"25%",label:"Yield Increase"}}];return(i,o)=>(a(),n("div",f,[t("section",g,[t("div",b,[t("div",K,[t("h1",k,s(i.$t("solutions.title")),1),t("p",E,s(i.$t("solutions.subtitle")),1)])])]),t("section",S,[t("div",w,[t("div",C,[(a(),n(c,null,r(u,e=>t("div",{key:e.id,class:"solution-card"},[t("div",V,s(e.icon),1),t("h3",I,s(i.$t(e.titleKey)),1),t("p",$,s(i.$t(e.descriptionKey)),1),t("div",B,[o[0]||(o[0]=t("h4",null,"Key Features:",-1)),t("ul",null,[(a(!0),n(c,null,r(e.features,l=>(a(),n("li",{key:l},s(l),1))),128))])]),t("div",F,[t("div",L,[t("span",N,s(e.stat.value),1),t("span",R,s(e.stat.label),1)])]),_(m(v),{to:"/contact",class:"btn btn-outline solution-cta"},{default:p(()=>o[1]||(o[1]=[h(" Learn More ")])),_:1,__:[1]})])),64))])])])]))}}),P=y(T,[["__scopeId","data-v-ff57328a"]]);export{P as default};
